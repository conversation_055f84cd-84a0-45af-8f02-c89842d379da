package main

import (
	"context"
	"fmt"
	"os"

	"github.com/zero-ops/service-system/internal/pkg/utils"
	"github.com/zero-ops/service-system/internal/worker"
)

// 测试 Worker 层通知功能
func main() {
	fmt.Println("🧪 测试 Worker 层通知功能")

	// 设置环境变量
	os.Setenv("ENV", "dev")

	// 加载 Worker 配置
	config := worker.LoadConfig()
	fmt.Printf("✅ Worker 配置加载成功\n")
	fmt.Printf("   Feishu 通知: enabled=%v\n", config.Notification.Feishu.Enabled)

	// 创建 Worker 实例
	workerInstance := worker.NewWorkerWithConfig(*config)
	fmt.Printf("✅ Worker 实例创建成功\n")

	// 测试通知管理器是否正确初始化
	if workerInstance.NotificationManager == nil {
		fmt.Printf("❌ Worker 通知管理器未初始化\n")
		return
	}

	total, enabled := workerInstance.NotificationManager.GetNotifierCount()
	fmt.Printf("✅ Worker 通知管理器初始化成功: %d 个通知器，%d 个已启用\n", total, enabled)

	// 发送测试通知
	fmt.Println("\n📤 发送测试通知...")
	err := workerInstance.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
		Level:     utils.NotificationLevelInfo,
		Title:     "Worker通知测试",
		Content:   "这是一条来自 Worker 层的测试通知",
		ServiceID: "test-worker",
		Metadata: map[string]interface{}{
			"test_type": "worker_notification_test",
			"version":   "1.0.0",
		},
		Timestamp: utils.GetCSTTimeString(),
	})

	if err != nil {
		fmt.Printf("❌ 发送通知失败: %v\n", err)
	} else {
		fmt.Printf("✅ 测试通知发送成功\n")
	}

	fmt.Println("\n🎉 Worker 层通知功能测试完成！")
}
