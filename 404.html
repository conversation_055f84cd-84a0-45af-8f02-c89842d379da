<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当前服务未启动 - 404</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #FFFFFF;
            color: #333333;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 代码风格背景 */
        .code-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.05;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #3485dc;
            padding: 20px;
            overflow: hidden;
            white-space: pre;
        }

        /* 科技感粒子背景 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #3485dc;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 主容器 */
        .container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            padding-top: 80px; /* 为通栏图形留出空间 */
            padding-bottom: 60px; /* 增加底部间隙，避免贴底 */
            position: relative;
            z-index: 1;
        }

        /* 主标题 */
        .main-title {
            font-size: 3.5rem;
            font-weight: 300;
            color: #3485dc;
            margin-bottom: 20px;
            text-align: center;
            letter-spacing: 2px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 20px rgba(52, 133, 220, 0.3);
            }
            to {
                text-shadow: 0 0 30px rgba(52, 133, 220, 0.6);
            }
        }

        /* 404科技感图片 */
        .error-code {
            margin-bottom: 30px;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite alternate;
        }

        .error-code svg {
            width: 300px;
            height: 120px;
            filter: drop-shadow(0 0 20px rgba(52, 133, 220, 0.3));
        }

        @keyframes pulse {
            from {
                transform: scale(1);
                filter: drop-shadow(0 0 20px rgba(52, 133, 220, 0.3));
            }
            to {
                transform: scale(1.05);
                filter: drop-shadow(0 0 30px rgba(52, 133, 220, 0.6));
            }
        }

        /* 内容卡片 */
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(52, 133, 220, 0.2);
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 10px 30px rgba(52, 133, 220, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 133, 220, 0.1), transparent);
            animation: scan 3s infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 描述文本 */
        .description {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
            text-align: center;
        }

        .description-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(52, 133, 220, 0.05);
            border-left: 3px solid #3485dc;
            border-radius: 5px;
        }

        /* 链接样式 */
        .contact-link {
            color: #3485dc;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .contact-link:hover {
            text-shadow: 0 0 10px rgba(52, 133, 220, 0.5);
            text-decoration: underline;
        }

        /* 按钮样式 */
        .action-button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(45deg, #3485dc, #2563eb);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(52, 133, 220, 0.3);
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 133, 220, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding-top: 80px; /* 移动端调整顶部间距 */
                padding-bottom: 40px; /* 移动端减少底部间隙 */
            }

            .header-banner {
                height: 50px; /* 移动端减少通栏高度 */
            }

            .header-content {
                padding: 0 20px;
            }

            .header-left {
                gap: 10px;
            }

            .tech-bracket {
                font-size: 1.2rem;
            }

            .tech-line {
                width: 50px;
            }

            .header-right {
                gap: 15px;
            }

            .header-dots {
                gap: 6px;
            }

            .header-dot:nth-child(1) {
                width: 6px;
                height: 6px;
            }

            .header-dot:nth-child(2) {
                width: 10px;
                height: 10px;
            }

            .header-dot:nth-child(3) {
                width: 8px;
                height: 8px;
            }

            .tech-code {
                font-size: 0.8rem;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .error-code svg {
                width: 250px;
                height: 100px;
            }

            .content-card {
                padding: 30px 20px;
                margin: 0 10px;
            }

            .description {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .error-code svg {
                width: 200px;
                height: 80px;
            }

            .main-title {
                font-size: 2rem;
            }
        }

        /* 静态通栏图形设计 */
        .header-banner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: linear-gradient(180deg,
                rgba(52, 133, 220, 0.12) 0%,
                rgba(52, 133, 220, 0.08) 30%,
                rgba(52, 133, 220, 0.04) 70%,
                rgba(52, 133, 220, 0.01) 90%,
                transparent 100%);
            backdrop-filter: blur(8px);
            z-index: 10;
            overflow: hidden;
        }

        .header-content {
            display: flex;
            align-items: center;
            height: 100%;
            position: relative;
            padding: 0 40px;
        }

        /* 左侧科技装饰 */
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .tech-bracket {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            color: #3485dc;
            opacity: 0.6;
            font-weight: bold;
        }

        .tech-dots {
            display: flex;
            gap: 4px;
        }

        .tech-dot {
            width: 6px;
            height: 6px;
            background: #3485dc;
            border-radius: 50%;
            opacity: 0.4;
        }

        .tech-dot:nth-child(2) {
            opacity: 0.7;
        }

        .tech-dot:nth-child(3) {
            opacity: 0.4;
        }

        .tech-line {
            width: 80px;
            height: 1px;
            background: linear-gradient(90deg, #3485dc, transparent);
            opacity: 0.5;
        }

        /* 右侧几何装饰 */
        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-dots {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-dot {
            background: #3485dc;
            border-radius: 50%;
            opacity: 0.5;
        }

        .header-dot:nth-child(1) {
            width: 8px;
            height: 8px;
            opacity: 0.4;
        }

        .header-dot:nth-child(2) {
            width: 12px;
            height: 12px;
            opacity: 0.6;
        }

        .header-dot:nth-child(3) {
            width: 10px;
            height: 10px;
            opacity: 0.5;
        }

        .tech-code {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #3485dc;
            opacity: 0.5;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- 静态通栏图形设计 -->
    <div class="header-banner">
        <div class="header-content">
            <!-- 左侧科技装饰 -->
            <div class="header-left">
                <div class="tech-bracket">[</div>
                <div class="tech-dots">
                    <div class="tech-dot"></div>
                    <div class="tech-dot"></div>
                    <div class="tech-dot"></div>
                </div>
                <div class="tech-line"></div>
            </div>

            <!-- 右侧装饰 -->
            <div class="header-right">
                <div class="tech-code">SYS_404</div>
                <div class="header-dots">
                    <div class="header-dot"></div>
                    <div class="header-dot"></div>
                    <div class="header-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 代码背景 -->
    <div class="code-background" id="codeBackground"></div>

    <div class="container">
        <div class="error-code">
            <svg viewBox="0 0 300 120" xmlns="http://www.w3.org/2000/svg">
                <!-- 404科技感设计 -->
                <!-- 数字4 -->
                <g fill="none" stroke="#3485dc" stroke-width="3">
                    <!-- 第一个4 -->
                    <path d="M20 20 L20 60 L40 60 M40 20 L40 80" stroke-linecap="round"/>
                    <circle cx="30" cy="30" r="2" fill="#3485dc" opacity="0.8">
                        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                    </circle>

                    <!-- 数字0 -->
                    <ellipse cx="80" cy="50" rx="20" ry="30" stroke-linecap="round"/>
                    <circle cx="70" cy="40" r="1.5" fill="#3485dc" opacity="0.6">
                        <animate attributeName="opacity" values="0.2;0.8;0.2" dur="1.5s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="90" cy="60" r="1.5" fill="#3485dc" opacity="0.6">
                        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="1.5s" repeatCount="indefinite"/>
                    </circle>

                    <!-- 第二个4 -->
                    <path d="M120 20 L120 60 L140 60 M140 20 L140 80" stroke-linecap="round"/>
                    <circle cx="130" cy="70" r="2" fill="#3485dc" opacity="0.8">
                        <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
                    </circle>
                </g>

                <!-- 科技感装饰线条 -->
                <g stroke="#3485dc" stroke-width="1" opacity="0.4">
                    <line x1="10" y1="90" x2="50" y2="90">
                        <animate attributeName="x2" values="10;50;10" dur="3s" repeatCount="indefinite"/>
                    </line>
                    <line x1="110" y1="90" x2="150" y2="90">
                        <animate attributeName="x2" values="110;150;110" dur="3s" repeatCount="indefinite"/>
                    </line>
                    <line x1="160" y1="30" x2="160" y2="70">
                        <animate attributeName="y2" values="30;70;30" dur="2.5s" repeatCount="indefinite"/>
                    </line>
                </g>

                <!-- 粒子效果 -->
                <g fill="#3485dc">
                    <circle cx="180" cy="25" r="1">
                        <animate attributeName="cy" values="25;95;25" dur="4s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="200" cy="40" r="1.5">
                        <animate attributeName="cy" values="40;80;40" dur="3s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="1;0;1" dur="3s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="220" cy="60" r="1">
                        <animate attributeName="cy" values="60;20;60" dur="3.5s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;1;0" dur="3.5s" repeatCount="indefinite"/>
                    </circle>
                </g>

                <!-- 网格背景 -->
                <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#3485dc" stroke-width="0.5" opacity="0.2"/>
                    </pattern>
                </defs>
                <rect width="300" height="120" fill="url(#grid)" opacity="0.3"/>

                <!-- 扫描线效果 -->
                <line x1="0" y1="60" x2="300" y2="60" stroke="#3485dc" stroke-width="1" opacity="0.6">
                    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="2s" repeatCount="indefinite"/>
                </line>
            </svg>
        </div>
        <h1 class="main-title">当前服务未启动</h1>

        <div class="content-card">
            <div class="description">
                <div class="description-item">
                    <strong>📧 未注册应用？</strong><br>
                    如果您的应用尚未注册，请联系管理员<br>
                    <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                </div>

                <div class="description-item">
                    <strong>⏰ 服务已停止？</strong><br>
                    如果您的应用已注册但超过12小时未使用，服务会被自动停止<br>
                    请重新进入登录页面激活服务
                    <br><br>
                    <a href="https://login.example.com" class="action-button">🚀 重新激活服务</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 创建代码背景
        function createCodeBackground() {
            const codeLines = [
                'function calculateMatrix(x, y, z) {',
                '  const alpha = Math.random() * 0.5;',
                '  const beta = x * y + z;',
                '  return { alpha, beta, gamma: alpha * beta };',
                '}',
                '',
                'class DataProcessor {',
                '  constructor(options = {}) {',
                '    this.buffer = new Array(256).fill(0);',
                '    this.threshold = options.threshold || 0.75;',
                '    this.iterations = 0;',
                '  }',
                '',
                '  async processChunk(data) {',
                '    for (let i = 0; i < data.length; i++) {',
                '      const value = data[i] * this.threshold;',
                '      this.buffer[i % 256] = value;',
                '    }',
                '    this.iterations++;',
                '  }',
                '',
                '  getMetrics() {',
                '    return {',
                '      processed: this.iterations,',
                '      efficiency: Math.random().toFixed(3),',
                '      status: this.iterations > 100 ? "optimal" : "warming"',
                '    };',
                '  }',
                '}',
                '',
                'const processor = new DataProcessor({',
                '  threshold: 0.85,',
                '  bufferSize: 512,',
                '  enableCache: true',
                '});',
                '',
                'async function runSimulation() {',
                '  const samples = generateSamples(1000);',
                '  await processor.processChunk(samples);',
                '  const metrics = processor.getMetrics();',
                '  console.log("Simulation complete:", metrics);',
                '}',
                '',
                'function generateSamples(count) {',
                '  return Array.from({ length: count }, () => ({',
                '    id: Math.random().toString(36).substr(2, 9),',
                '    value: Math.random() * 100,',
                '    timestamp: Date.now() + Math.random() * 1000',
                '  }));',
                '}',
                '',
                'export { DataProcessor, calculateMatrix };'
            ];

            const codeBackground = document.getElementById('codeBackground');
            const repeatedCode = Array(20).fill(codeLines.join('\n')).join('\n\n');
            codeBackground.textContent = repeatedCode;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            createCodeBackground();
        });

        // 添加鼠标移动效果
        document.addEventListener('mousemove', function(e) {
            const particles = document.querySelectorAll('.particle');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            particles.forEach((particle, index) => {
                const speed = (index % 3 + 1) * 0.5;
                const x = (mouseX - 0.5) * speed;
                const y = (mouseY - 0.5) * speed;
                particle.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
    </script>
</body>
</html>
