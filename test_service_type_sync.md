# Service Type 同步测试验证

## 测试目的
验证 `service_type` 字段在 Service 层和 Worker 层之间的正确同步。

## 测试场景

### 1. 数据库表结构验证
```sql
-- 检查 deploy_record_w 表是否包含 service_type 字段
PRAGMA table_info(deploy_record_w);

-- 预期结果：应该看到 service_type 字段
```

### 2. 新服务部署测试
```bash
# 创建一个 TRIAL 类型的服务
curl -X POST http://localhost:8080/api/services \
  -H "Content-Type: application/json" \
  -d '{
    "service_info": {
      "service_type": "TRIAL",
      "image_name": "nginx",
      "domain_prefix": "test-trial"
    }
  }'
```

### 3. 数据同步验证
```sql
-- Service 层查询
SELECT service_id, service_type FROM deploy_record WHERE service_id = 'test-service-id';

-- Worker 层查询
SELECT service_id, service_type FROM deploy_record_w WHERE service_id = 'test-service-id';

-- 预期结果：两个表中的 service_type 应该一致
```

### 4. 活跃度检查验证
```bash
# 检查 Worker 层日志，确认 isTrialService 方法能正确识别 TRIAL 服务
tail -f /var/log/worker.log | grep "activity check"

# 预期结果：
# - TRIAL 服务应该进入活跃度检查
# - 非 TRIAL 服务应该被跳过，但日志中应该显示正确的服务类型
```

## 验证要点

1. **数据库迁移**：现有表应该自动添加 `service_type` 字段
2. **数据传递**：Service → Worker 的 API 调用应该包含 `service_type`
3. **数据存储**：Worker 层应该正确存储 `service_type`
4. **功能恢复**：`isTrialService` 方法应该能正确工作

## 回滚方案

如果测试失败，可以通过以下步骤回滚：

1. 移除 `deploy_record_w` 表的 `service_type` 字段
2. 恢复 `WorkerDeployRequest` 结构体
3. 恢复相关的数据库操作代码
