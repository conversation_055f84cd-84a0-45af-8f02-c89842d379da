package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "modernc.org/sqlite"
)

// DatabaseIndexMigration 数据库索引迁移工具
// 用于为现有的 SQLite 数据库添加性能优化索引
func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run add_database_indexes.go <数据库文件路径>")
		fmt.Println("示例: go run add_database_indexes.go ./dbdata/service.db")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	
	// 检查数据库文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("数据库文件不存在: %s", dbPath)
	}

	// 备份原数据库
	backupPath := dbPath + ".backup." + fmt.Sprintf("%d", os.Getpid())
	if err := copyFile(dbPath, backupPath); err != nil {
		log.Fatalf("备份数据库失败: %v", err)
	}
	fmt.Printf("✅ 数据库已备份到: %s\n", backupPath)

	// 连接数据库
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	fmt.Printf("🔗 成功连接到数据库: %s\n", dbPath)

	// 添加索引
	if err := addIndexes(db); err != nil {
		log.Fatalf("添加索引失败: %v", err)
	}

	fmt.Println("🎉 所有索引添加完成！")
	fmt.Println("📊 建议运行 ANALYZE 命令来更新统计信息:")
	fmt.Printf("   sqlite3 %s \"ANALYZE;\"\n", dbPath)
}

// addIndexes 添加所有必要的索引
func addIndexes(db *sql.DB) error {
	indexes := []struct {
		name        string
		description string
		sql         string
	}{
		{
			name:        "idx_deploy_record_status",
			description: "deploy_record 表状态索引",
			sql:         "CREATE INDEX IF NOT EXISTS idx_deploy_record_status ON deploy_record(status)",
		},
		{
			name:        "idx_deploy_record_updated_at",
			description: "deploy_record 表更新时间索引",
			sql:         "CREATE INDEX IF NOT EXISTS idx_deploy_record_updated_at ON deploy_record(updated_at)",
		},
		{
			name:        "idx_deploy_record_status_updated_at",
			description: "deploy_record 表状态+更新时间复合索引 (OSS统计优化)",
			sql:         "CREATE INDEX IF NOT EXISTS idx_deploy_record_status_updated_at ON deploy_record(status, updated_at DESC)",
		},
		{
			name:        "idx_deploy_record_service_id",
			description: "deploy_record 表服务ID索引",
			sql:         "CREATE INDEX IF NOT EXISTS idx_deploy_record_service_id ON deploy_record(service_id)",
		},
		{
			name:        "idx_deploy_record_queue_optimization",
			description: "deploy_record 表队列查询优化索引",
			sql:         "CREATE INDEX IF NOT EXISTS idx_deploy_record_queue_optimization ON deploy_record(status, service_type, updated_at ASC)",
		},
		{
			name:        "idx_service_resource_usage_updated_at",
			description: "service_resource_usage 表更新时间索引",
			sql:         "CREATE INDEX IF NOT EXISTS idx_service_resource_usage_updated_at ON service_resource_usage(updated_at)",
		},
	}

	fmt.Printf("📝 开始添加 %d 个索引...\n", len(indexes))

	for i, idx := range indexes {
		fmt.Printf("  [%d/%d] 创建索引: %s - %s\n", i+1, len(indexes), idx.name, idx.description)
		
		if _, err := db.Exec(idx.sql); err != nil {
			return fmt.Errorf("创建索引 %s 失败: %w", idx.name, err)
		}
		
		fmt.Printf("    ✅ 成功\n")
	}

	return nil
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
