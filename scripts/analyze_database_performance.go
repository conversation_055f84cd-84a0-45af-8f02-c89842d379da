package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "modernc.org/sqlite"
)

// DatabasePerformanceAnalyzer 数据库性能分析工具
// 用于分析 SQLite 数据库的索引使用情况和查询性能
func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run analyze_database_performance.go <数据库文件路径>")
		fmt.Println("示例: go run analyze_database_performance.go ./dbdata/service.db")
		os.Exit(1)
	}

	dbPath := os.Args[1]

	// 检查数据库文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("数据库文件不存在: %s", dbPath)
	}

	// 连接数据库
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	fmt.Printf("🔗 成功连接到数据库: %s\n", dbPath)
	fmt.Println(strings.Repeat("=", 80))

	// 分析数据库
	if err := analyzeDatabase(db); err != nil {
		log.Fatalf("分析数据库失败: %v", err)
	}
}

// analyzeDatabase 分析数据库性能
func analyzeDatabase(db *sql.DB) error {
	// 1. 显示表信息
	if err := showTableInfo(db); err != nil {
		return fmt.Errorf("显示表信息失败: %w", err)
	}

	// 2. 显示索引信息
	if err := showIndexInfo(db); err != nil {
		return fmt.Errorf("显示索引信息失败: %w", err)
	}

	// 3. 测试关键查询性能
	if err := testQueryPerformance(db); err != nil {
		return fmt.Errorf("测试查询性能失败: %w", err)
	}

	return nil
}

// showTableInfo 显示表信息
func showTableInfo(db *sql.DB) error {
	fmt.Println("📊 表信息统计:")

	tables := []string{"deploy_record", "workers", "image_type", "service_resource_usage"}

	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count)
		if err != nil {
			if strings.Contains(err.Error(), "no such table") {
				fmt.Printf("  ❌ %s: 表不存在\n", table)
				continue
			}
			return fmt.Errorf("查询表 %s 记录数失败: %w", table, err)
		}
		fmt.Printf("  📋 %s: %d 条记录\n", table, count)
	}

	fmt.Println()
	return nil
}

// showIndexInfo 显示索引信息
func showIndexInfo(db *sql.DB) error {
	fmt.Println("🔍 索引信息:")

	// 查询所有索引
	rows, err := db.Query(`
		SELECT name, tbl_name, sql 
		FROM sqlite_master 
		WHERE type = 'index' 
		AND name NOT LIKE 'sqlite_%'
		ORDER BY tbl_name, name
	`)
	if err != nil {
		return fmt.Errorf("查询索引信息失败: %w", err)
	}
	defer rows.Close()

	currentTable := ""
	for rows.Next() {
		var indexName, tableName, sql string
		if err := rows.Scan(&indexName, &tableName, &sql); err != nil {
			return fmt.Errorf("扫描索引信息失败: %w", err)
		}

		if tableName != currentTable {
			if currentTable != "" {
				fmt.Println()
			}
			fmt.Printf("  📋 表: %s\n", tableName)
			currentTable = tableName
		}

		fmt.Printf("    🔍 %s\n", indexName)
		if sql != "" {
			fmt.Printf("        SQL: %s\n", sql)
		}
	}

	fmt.Println()
	return nil
}

// testQueryPerformance 测试关键查询性能
func testQueryPerformance(db *sql.DB) error {
	fmt.Println("⚡ 查询性能测试:")

	queries := []struct {
		name        string
		description string
		sql         string
		params      []interface{}
	}{
		{
			name:        "硬件资源统计查询",
			description: "getRunningServices 使用的查询",
			sql:         "SELECT COUNT(*) FROM deploy_record WHERE status IN ('RUNNING', 'PROCESSING')",
			params:      nil,
		},
		{
			name:        "OSS统计查询",
			description: "getRunningServicesForOSS 使用的查询",
			sql: `SELECT COUNT(*) FROM deploy_record 
				  WHERE status IN ('RUNNING', 'PROCESSING') 
				  OR (status IN ('STOPPED') AND updated_at > ?)`,
			params: []interface{}{time.Now().AddDate(0, 0, -1).Format("2006-01-02 15:04:05")},
		},
		{
			name:        "队列查询",
			description: "GetNextQueueingRecord 使用的查询",
			sql:         `SELECT COUNT(*) FROM deploy_record WHERE status = 'QUEUEING'`,
			params:      nil,
		},
		{
			name:        "服务ID查询",
			description: "按服务ID查询",
			sql:         `SELECT COUNT(*) FROM deploy_record WHERE service_id LIKE 'test%'`,
			params:      nil,
		},
	}

	for _, query := range queries {
		fmt.Printf("  🔍 %s (%s)\n", query.name, query.description)

		// 使用 EXPLAIN QUERY PLAN 分析查询计划
		explainSQL := "EXPLAIN QUERY PLAN " + query.sql
		start := time.Now()

		rows, err := db.Query(explainSQL, query.params...)
		if err != nil {
			fmt.Printf("    ❌ 查询计划分析失败: %v\n", err)
			continue
		}

		fmt.Printf("    📋 查询计划:\n")
		for rows.Next() {
			var id, parent, notused int
			var detail string
			if err := rows.Scan(&id, &parent, &notused, &detail); err != nil {
				fmt.Printf("    ❌ 扫描查询计划失败: %v\n", err)
				break
			}
			fmt.Printf("      %s\n", detail)
		}
		rows.Close()

		// 执行实际查询测试性能
		start = time.Now()
		var count int
		err = db.QueryRow(query.sql, query.params...).Scan(&count)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("    ❌ 查询执行失败: %v\n", err)
		} else {
			fmt.Printf("    ⏱️  执行时间: %v (结果: %d 条记录)\n", duration, count)
		}

		fmt.Println()
	}

	return nil
}
