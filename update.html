<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务升级中 - 请稍候</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #FFFFFF;
            color: #333333;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 代码风格背景 */
        .code-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.05;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #3485dc;
            padding: 20px;
            overflow: hidden;
            white-space: pre;
        }

        /* 科技感粒子背景 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #3485dc;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 主容器 */
        .container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            padding-top: 80px;
            padding-bottom: 60px;
            position: relative;
            z-index: 1;
        }

        /* 主标题 */
        .main-title {
            font-size: 3.5rem;
            font-weight: 300;
            color: #3485dc;
            margin-bottom: 20px;
            text-align: center;
            letter-spacing: 2px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 20px rgba(52, 133, 220, 0.3);
            }
            to {
                text-shadow: 0 0 30px rgba(52, 133, 220, 0.6);
            }
        }

        /* 升级图标 */
        .update-icon {
            margin-bottom: 30px;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite alternate;
        }

        .update-icon svg {
            width: 300px;
            height: 120px;
            filter: drop-shadow(0 0 20px rgba(52, 133, 220, 0.3));
        }

        @keyframes pulse {
            from {
                transform: scale(1);
                filter: drop-shadow(0 0 20px rgba(52, 133, 220, 0.3));
            }
            to {
                transform: scale(1.05);
                filter: drop-shadow(0 0 30px rgba(52, 133, 220, 0.6));
            }
        }

        /* 内容卡片 */
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(52, 133, 220, 0.2);
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 10px 30px rgba(52, 133, 220, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 133, 220, 0.1), transparent);
            animation: scan 3s infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 描述文本 */
        .description {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
            text-align: center;
        }

        .description-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(52, 133, 220, 0.05);
            border-left: 3px solid #3485dc;
            border-radius: 5px;
        }

        /* 进度条 */
        .progress-container {
            margin: 30px 0;
            background: rgba(52, 133, 220, 0.1);
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #3485dc, #2563eb);
            border-radius: 10px;
            animation: progress 3s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 20%; }
            50% { width: 80%; }
            100% { width: 20%; }
        }

        /* 状态指示器 */
        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #3485dc;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }

        .status-dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .status-dot:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes blink {
            0%, 50% { opacity: 0.3; }
            25% { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2.5rem;
            }

            .update-icon svg {
                width: 250px;
                height: 100px;
            }

            .content-card {
                padding: 30px 20px;
                margin: 0 10px;
            }

            .description {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .update-icon svg {
                width: 200px;
                height: 80px;
            }

            .main-title {
                font-size: 2rem;
            }
        }

        /* 静态通栏图形设计 */
        .header-banner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: linear-gradient(180deg,
                rgba(52, 133, 220, 0.12) 0%,
                rgba(52, 133, 220, 0.08) 30%,
                rgba(52, 133, 220, 0.04) 70%,
                rgba(52, 133, 220, 0.01) 90%,
                transparent 100%);
            backdrop-filter: blur(8px);
            z-index: 10;
            overflow: hidden;
        }

        .header-content {
            display: flex;
            align-items: center;
            height: 100%;
            position: relative;
            padding: 0 40px;
        }

        /* 左侧科技装饰 */
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .tech-bracket {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            color: #3485dc;
            opacity: 0.6;
            font-weight: bold;
        }

        .tech-dots {
            display: flex;
            gap: 4px;
        }

        .tech-dot {
            width: 6px;
            height: 6px;
            background: #3485dc;
            border-radius: 50%;
            opacity: 0.4;
        }

        .tech-dot:nth-child(2) {
            opacity: 0.7;
        }

        .tech-dot:nth-child(3) {
            opacity: 0.4;
        }

        .tech-line {
            width: 80px;
            height: 1px;
            background: linear-gradient(90deg, #3485dc, transparent);
            opacity: 0.5;
        }

        /* 右侧几何装饰 */
        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-dots {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-dot {
            background: #3485dc;
            border-radius: 50%;
            opacity: 0.5;
        }

        .header-dot:nth-child(1) {
            width: 8px;
            height: 8px;
            opacity: 0.4;
        }

        .header-dot:nth-child(2) {
            width: 12px;
            height: 12px;
            opacity: 0.6;
        }

        .header-dot:nth-child(3) {
            width: 10px;
            height: 10px;
            opacity: 0.5;
        }

        .tech-code {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #3485dc;
            opacity: 0.5;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- 静态通栏图形设计 -->
    <div class="header-banner">
        <div class="header-content">
            <!-- 左侧科技装饰 -->
            <div class="header-left">
                <div class="tech-bracket">[</div>
                <div class="tech-dots">
                    <div class="tech-dot"></div>
                    <div class="tech-dot"></div>
                    <div class="tech-dot"></div>
                </div>
                <div class="tech-line"></div>
            </div>

            <!-- 右侧装饰 -->
            <div class="header-right">
                <div class="tech-code">SYS_UPD</div>
                <div class="header-dots">
                    <div class="header-dot"></div>
                    <div class="header-dot"></div>
                    <div class="header-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 代码背景 -->
    <div class="code-background" id="codeBackground"></div>

    <div class="container">
        <div class="update-icon">
            <svg viewBox="0 0 300 120" xmlns="http://www.w3.org/2000/svg">
                <!-- 升级图标设计 -->
                <!-- 精致齿轮图标 -->
                <defs>
                    <!-- 齿轮渐变 -->
                    <linearGradient id="gearGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#3485dc;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#60a5fa;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#3485dc;stop-opacity:0.6" />
                    </linearGradient>

                    <!-- 内圈渐变 -->
                    <radialGradient id="innerGradient" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#3485dc;stop-opacity:0.3" />
                        <stop offset="70%" style="stop-color:#3485dc;stop-opacity:0.1" />
                        <stop offset="100%" style="stop-color:#3485dc;stop-opacity:0.8" />
                    </radialGradient>
                </defs>

                <g transform="translate(80,60)">
                    <!-- 外圈齿轮齿 -->
                    <g fill="url(#gearGradient)" stroke="#3485dc" stroke-width="1.5" opacity="0.9">
                        <!-- 12个精致的齿 -->
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(0)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(30)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(60)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(90)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(120)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(150)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(180)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(210)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(240)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(270)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(300)"/>
                        <path d="M 0,-28 L -3,-35 L 3,-35 Z" transform="rotate(330)"/>
                    </g>

                    <!-- 主齿轮环 -->
                    <circle r="28" fill="none" stroke="url(#gearGradient)" stroke-width="2.5" opacity="0.8"/>

                    <!-- 中间环 -->
                    <circle r="20" fill="url(#innerGradient)" stroke="#3485dc" stroke-width="1.5" opacity="0.6"/>

                    <!-- 内圈装饰环 -->
                    <circle r="15" fill="none" stroke="#3485dc" stroke-width="1" opacity="0.4"/>

                    <!-- 中心圆 -->
                    <circle r="8" fill="url(#gearGradient)" stroke="#3485dc" stroke-width="1.5" opacity="0.9"/>

                    <!-- 中心装饰点 -->
                    <circle r="3" fill="#3485dc" opacity="0.8"/>

                    <!-- 装饰线条 -->
                    <g stroke="#3485dc" stroke-width="1" opacity="0.5">
                        <line x1="-12" y1="0" x2="-6" y2="0"/>
                        <line x1="6" y1="0" x2="12" y2="0"/>
                        <line x1="0" y1="-12" x2="0" y2="-6"/>
                        <line x1="0" y1="6" x2="0" y2="12"/>
                        <line x1="-8.5" y1="-8.5" x2="-4.2" y2="-4.2"/>
                        <line x1="4.2" y1="4.2" x2="8.5" y2="8.5"/>
                        <line x1="8.5" y1="-8.5" x2="4.2" y2="-4.2"/>
                        <line x1="-4.2" y1="4.2" x2="-8.5" y2="8.5"/>
                    </g>

                    <!-- 旋转动画 -->
                    <animateTransform attributeName="transform" type="rotate"
                        values="0 80 60;360 80 60" dur="6s" repeatCount="indefinite"/>
                </g>

                <!-- 精致上传箭头 -->
                <g transform="translate(150,60)">
                    <!-- 箭头主体 -->
                    <g fill="none" stroke="#3485dc" stroke-width="2.5" stroke-linecap="round">
                        <!-- 箭头杆 -->
                        <line x1="0" y1="15" x2="0" y2="-10" opacity="0.8"/>
                        <line x1="0" y1="25" x2="0" y2="30" opacity="0.6"/>

                        <!-- 箭头头部 -->
                        <path d="M -8,0 L 0,-10 L 8,0" fill="none" stroke-linejoin="round"/>

                        <!-- 装饰线条 -->
                        <g opacity="0.5" stroke-width="1.5">
                            <line x1="-12" y1="5" x2="-6" y2="-1"/>
                            <line x1="12" y1="5" x2="6" y2="-1"/>
                            <line x1="-10" y1="15" x2="-4" y2="9"/>
                            <line x1="10" y1="15" x2="4" y2="9"/>
                        </g>
                    </g>

                    <!-- 上升粒子效果 -->
                    <g fill="#3485dc" opacity="0.6">
                        <circle cx="-15" cy="10" r="1">
                            <animate attributeName="cy" values="10;-20;10" dur="2.5s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0;0.8;0" dur="2.5s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="15" cy="5" r="1.5">
                            <animate attributeName="cy" values="5;-25;5" dur="3s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0;0.6;0" dur="3s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="0" cy="20" r="1">
                            <animate attributeName="cy" values="20;-15;20" dur="2s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
                        </circle>
                    </g>

                    <!-- 箭头动画 -->
                    <animateTransform attributeName="transform" type="translateY"
                        values="0;-3;0" dur="2.5s" repeatCount="indefinite"/>
                </g>

                <!-- 精致进度条 -->
                <g transform="translate(200,50)">
                    <!-- 进度条背景 -->
                    <rect x="0" y="-4" width="80" height="8" rx="4" fill="none" stroke="#3485dc"
                          stroke-width="1.5" opacity="0.3"/>

                    <!-- 进度条填充 -->
                    <rect x="2" y="-2" width="0" height="4" rx="2" fill="url(#gearGradient)" opacity="0.9">
                        <animate attributeName="width" values="0;76;0" dur="4s" repeatCount="indefinite"/>
                    </rect>

                    <!-- 进度条光效 -->
                    <rect x="0" y="-3" width="4" height="6" rx="3" fill="#3485dc" opacity="0.8">
                        <animate attributeName="x" values="-4;80;-4" dur="4s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite"/>
                    </rect>

                    <!-- 进度百分比装饰 -->
                    <g fill="#3485dc" opacity="0.6" font-family="monospace" font-size="8" text-anchor="middle">
                        <text x="40" y="-8">
                            <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
                            UPDATING...
                        </text>
                    </g>

                    <!-- 装饰点 -->
                    <g fill="#3485dc" opacity="0.5">
                        <circle cx="-8" cy="0" r="1">
                            <animate attributeName="opacity" values="0.3;0.8;0.3" dur="1.5s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="88" cy="0" r="1">
                            <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1.5s" repeatCount="indefinite"/>
                        </circle>
                    </g>
                </g>

                <!-- 精致科技装饰线条 -->
                <g stroke="#3485dc" stroke-width="1.5" opacity="0.5" stroke-linecap="round">
                    <!-- 左侧连接线 -->
                    <g>
                        <line x1="20" y1="90" x2="20" y2="90">
                            <animate attributeName="x2" values="20;65;20" dur="3s" repeatCount="indefinite"/>
                        </line>
                        <circle cx="20" cy="90" r="2" fill="#3485dc" opacity="0.6"/>
                        <circle cx="65" cy="90" r="1.5" fill="#3485dc" opacity="0.4">
                            <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3s" repeatCount="indefinite"/>
                        </circle>
                    </g>

                    <!-- 右侧连接线 -->
                    <g>
                        <line x1="120" y1="90" x2="120" y2="90">
                            <animate attributeName="x2" values="120;165;120" dur="3.5s" repeatCount="indefinite"/>
                        </line>
                        <circle cx="120" cy="90" r="2" fill="#3485dc" opacity="0.6"/>
                        <circle cx="165" cy="90" r="1.5" fill="#3485dc" opacity="0.4">
                            <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3.5s" repeatCount="indefinite"/>
                        </circle>
                    </g>

                    <!-- 垂直数据流 -->
                    <g>
                        <line x1="240" y1="30" x2="240" y2="30">
                            <animate attributeName="y2" values="30;75;30" dur="2.8s" repeatCount="indefinite"/>
                        </line>
                        <circle cx="240" cy="30" r="2" fill="#3485dc" opacity="0.6"/>
                        <circle cx="240" cy="75" r="1.5" fill="#3485dc" opacity="0.4">
                            <animate attributeName="opacity" values="0.2;0.8;0.2" dur="2.8s" repeatCount="indefinite"/>
                        </circle>
                    </g>
                </g>

                <!-- 精致粒子效果 -->
                <g fill="#3485dc">
                    <!-- 左侧粒子群 -->
                    <circle cx="30" cy="25" r="1.5" opacity="0.8">
                        <animate attributeName="cy" values="25;95;25" dur="4.5s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;0.8;0" dur="4.5s" repeatCount="indefinite"/>
                        <animate attributeName="r" values="1.5;1;1.5" dur="4.5s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="35" cy="30" r="1" opacity="0.6">
                        <animate attributeName="cy" values="30;90;30" dur="5s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;0.6;0" dur="5s" repeatCount="indefinite"/>
                    </circle>

                    <!-- 中间粒子群 -->
                    <circle cx="180" cy="40" r="2" opacity="0.7">
                        <animate attributeName="cy" values="40;80;40" dur="3.2s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.7;0;0.7" dur="3.2s" repeatCount="indefinite"/>
                        <animate attributeName="r" values="2;1.5;2" dur="3.2s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="185" cy="35" r="1" opacity="0.5">
                        <animate attributeName="cy" values="35;85;35" dur="3.8s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.5;0;0.5" dur="3.8s" repeatCount="indefinite"/>
                    </circle>

                    <!-- 右侧粒子群 -->
                    <circle cx="270" cy="60" r="1.5" opacity="0.8">
                        <animate attributeName="cy" values="60;20;60" dur="3.5s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;0.8;0" dur="3.5s" repeatCount="indefinite"/>
                        <animate attributeName="r" values="1.5;1;1.5" dur="3.5s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="275" cy="65" r="1" opacity="0.6">
                        <animate attributeName="cy" values="65;15;65" dur="4s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0;0.6;0" dur="4s" repeatCount="indefinite"/>
                    </circle>
                </g>

                <!-- 网格背景 -->
                <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#3485dc" stroke-width="0.5" opacity="0.2"/>
                    </pattern>
                </defs>
                <rect width="300" height="120" fill="url(#grid)" opacity="0.3"/>

                <!-- 扫描线效果 -->
                <line x1="0" y1="60" x2="300" y2="60" stroke="#3485dc" stroke-width="1" opacity="0.6">
                    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="2s" repeatCount="indefinite"/>
                </line>
            </svg>
        </div>
        <h1 class="main-title">服务升级中</h1>

        <div class="content-card">
            <div class="description">
                <div class="description-item">
                    <strong>🔄 正在升级服务</strong><br>
                    系统正在为您升级服务到最新版本<br>
                    预计需要 5-10 分钟，请耐心等待
                </div>

                <div class="progress-container">
                    <div class="progress-bar"></div>
                </div>

                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <div class="status-dot"></div>
                    <div class="status-dot"></div>
                </div>

                <div class="description-item">
                    <strong>⏰ 升级完成后</strong><br>
                    页面将自动刷新并跳转到新版本<br>
                    如果长时间未响应，请手动刷新页面
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 创建代码背景
        function createCodeBackground() {
            const codeLines = [
                'async function updateContainer(serviceId, imageUrl) {',
                '  console.log(`Starting update for ${serviceId}...`);',
                '  const oldContainer = await docker.getContainer(serviceId);',
                '  await oldContainer.stop();',
                '  await oldContainer.remove();',
                '',
                '  const newContainer = await docker.createContainer({',
                '    name: serviceId,',
                '    image: imageUrl,',
                '    restartPolicy: { Name: "always" },',
                '    networkMode: "bridge"',
                '  });',
                '',
                '  await newContainer.start();',
                '  console.log(`Update completed for ${serviceId}`);',
                '}',
                '',
                'class UpdateManager {',
                '  constructor(options = {}) {',
                '    this.maxRetries = options.maxRetries || 3;',
                '    this.timeout = options.timeout || 300000;',
                '    this.status = "idle";',
                '  }',
                '',
                '  async performUpdate(config) {',
                '    this.status = "updating";',
                '    const startTime = Date.now();',
                '',
                '    try {',
                '      await this.validateConfig(config);',
                '      await this.backupCurrentState();',
                '      await this.executeUpdate(config);',
                '      await this.verifyUpdate();',
                '      this.status = "completed";',
                '    } catch (error) {',
                '      await this.rollback();',
                '      this.status = "failed";',
                '      throw error;',
                '    }',
                '',
                '    const duration = Date.now() - startTime;',
                '    console.log(`Update completed in ${duration}ms`);',
                '  }',
                '',
                '  async validateConfig(config) {',
                '    if (!config.serviceId || !config.imageUrl) {',
                '      throw new Error("Invalid configuration");',
                '    }',
                '    return true;',
                '  }',
                '}',
                '',
                'const updateManager = new UpdateManager({',
                '  maxRetries: 3,',
                '  timeout: 300000',
                '});',
                '',
                'export { UpdateManager, updateContainer };'
            ];

            const codeBackground = document.getElementById('codeBackground');
            const repeatedCode = Array(15).fill(codeLines.join('\n')).join('\n\n');
            codeBackground.textContent = repeatedCode;
        }

        // 自动刷新检测
        function checkUpdateStatus() {
            // 每30秒检查一次服务状态
            setInterval(async () => {
                try {
                    const response = await fetch(window.location.href, {
                        method: 'HEAD',
                        cache: 'no-cache'
                    });

                    // 如果返回200且不是升级页面，说明升级完成
                    if (response.ok && !response.url.includes('update.html')) {
                        window.location.reload();
                    }
                } catch (error) {
                    // 网络错误，继续等待
                    console.log('Checking update status...');
                }
            }, 30000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            createCodeBackground();
            checkUpdateStatus();
        });

        // 添加鼠标移动效果
        document.addEventListener('mousemove', function(e) {
            const particles = document.querySelectorAll('.particle');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            particles.forEach((particle, index) => {
                const speed = (index % 3 + 1) * 0.5;
                const x = (mouseX - 0.5) * speed;
                const y = (mouseY - 0.5) * speed;
                particle.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
    </script>
</body>
</html>
