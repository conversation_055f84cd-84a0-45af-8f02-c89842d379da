# Zero Ops Service System - Worker Configuration
# Worker 层默认配置文件

# 通知系统配置
notification:
  # 飞书通知配置
  feishu:
    enabled: true
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/ebe8099d-fe6b-4283-b0f1-3a5ae803cbc9"
    user_map: {
      "杨翰文": "ou_81746e1e46e27da95ce4efcca73667ab",
      "卢鑫悦": "ou_adcf57c9df041f77766667ffa55710bc",
      "武奔": "ou_4cbd5c70b100d3ea4f6a54b937b10c61",
      "黎冠鹏": "ou_62e0c6f099893145a9e90bbb33eba76e",
      "于松伟": "ou_575990086855cc1ccdee65f938ef014c",
      "周斌鑫": "ou_a6c9af077c690c91b0232c080aadcfd7"
    }
  
  # 微信通知配置
  wechat:
    enabled: false
    webhook_url: ""
    app_id: ""
    secret: ""
  
  # 钉钉通知配置
  dingtalk:
    enabled: false
    webhook_url: ""
    access_token: ""
  
  # 邮件通知配置
  email:
    enabled: false
    smtp_host: ""
    smtp_port: 587
    username: ""
    password: ""
    from_name: "Zero Ops Worker"
    to_addresses: []

# Worker 配置
worker:
  log_dir: "log"
  
# 数据库配置
database:
  path: "./dbdata/worker.db"
  
# SSH 配置
ssh:
  default_user: "root"
  default_port: 22
  timeout: 10
  default_key_path: "dbdata/rsa.pem"

# 日志配置
logging:
  level: "info"
  file: "./logs/worker.log"
