package worker

import (
	"context"
	"log"
	"os"
	"sync"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// NewWorker creates a new worker instance
func NewWorker() *Worker {
	config := DefaultConfig()
	return NewWorkerWithConfig(config)
}

// NewWorkerWithConfig creates a new worker instance with the given configuration
func NewWorkerWithConfig(config Config) *Worker {
	// 🆕 在初始化时获取并缓存Worker节点的IP
	workerIP := getWorkerIP()

	// Create worker instance
	worker := &Worker{
		config:               config,
		taskProcessorMu:      sync.Mutex{},
		taskProcessorRunning: false,
		taskProcessorCancel:  nil,
		workerIP:             workerIP, // 🆕 缓存Worker IP
		// 🆕 基于配置初始化通知管理器
		NotificationManager: utils.CreateNotificationManagerFromConfig(utils.NotificationConfig{
			Feishu: utils.FeishuNotificationConfig{
				Enabled:    config.Notification.Feishu.Enabled,
				WebhookURL: config.Notification.Feishu.WebhookURL,
				UserMap:    config.Notification.Feishu.UserMap,
			},
			WeChat: utils.WeChatNotificationConfig{
				Enabled:    config.Notification.WeChat.Enabled,
				WebhookURL: config.Notification.WeChat.WebhookURL,
				AppID:      config.Notification.WeChat.AppID,
				Secret:     config.Notification.WeChat.Secret,
			},
			DingTalk: utils.DingTalkNotificationConfig{
				Enabled:     config.Notification.DingTalk.Enabled,
				WebhookURL:  config.Notification.DingTalk.WebhookURL,
				AccessToken: config.Notification.DingTalk.AccessToken,
			},
			Email: utils.EmailNotificationConfig{
				Enabled:     config.Notification.Email.Enabled,
				SMTPHost:    config.Notification.Email.SMTPHost,
				SMTPPort:    config.Notification.Email.SMTPPort,
				Username:    config.Notification.Email.Username,
				Password:    config.Notification.Email.Password,
				FromName:    config.Notification.Email.FromName,
				ToAddresses: config.Notification.Email.ToAddresses,
			},
		}),
	}

	// 🆕 记录Worker IP和通知管理器初始化状态
	log.Printf("Worker initialized with IP: %s", workerIP)
	total, enabled := worker.NotificationManager.GetNotifierCount()
	log.Printf("Worker notification manager initialized: %d total notifiers, %d enabled", total, enabled)
	if enabled > 0 {
		log.Printf("Worker enabled notifiers: %v", worker.NotificationManager.GetEnabledNotifiers())
	}

	// Initialize database
	dbConfig := database.WorkerDBConfig{
		DBPath: config.DB.DBPath,
	}

	db, err := database.NewWorkerDB(dbConfig)
	if err != nil {
		log.Printf("Failed to initialize database: %v", err)
	} else {
		// Initialize schema
		if err := db.InitSchema(); err != nil {
			log.Printf("Failed to initialize database schema: %v", err)
		}

		// Initialize repositories
		worker.db = db
		worker.deployRecordWRepo = database.NewDeployRecordWRepository(db)

		// Start the task processor
		worker.StartTaskProcessor(context.Background())
		log.Println("Task processor started")
	}

	// Set initial worker status to AVAILABLE
	SetWorkerStatus(StatusAvailable)

	return worker
}

// 🆕 获取Worker节点IP的辅助函数
func getWorkerIP() string {
	// 优先从环境变量获取
	if ip := os.Getenv("IP"); ip != "" {
		return ip
	}

	// 默认值
	return "unknown"
}

// Close closes the worker and releases resources
func (w *Worker) Close() error {
	// Stop the task processor
	w.StopTaskProcessor()

	// Close the database connection
	if w.db != nil {
		return w.db.Close()
	}

	return nil
}
