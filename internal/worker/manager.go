package worker

import (
	"context"
	"log"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
)

// WorkerManager 处理 worker 节点的所有业务逻辑
type WorkerManager struct {
	worker *Worker
	config Config
}

// NewWorkerManager 创建一个新的 WorkerManager 实例
func NewWorkerManager() *WorkerManager {
	return NewWorkerManagerWithConfig(DefaultConfig())
}

// NewWorkerManagerWithConfig 使用指定配置创建一个新的 WorkerManager 实例
func NewWorkerManagerWithConfig(config Config) *WorkerManager {
	// 🎯 创建Worker实例（内部会初始化通知管理器）
	workerInstance := NewWorkerWithConfig(config)

	manager := &WorkerManager{
		worker: workerInstance,
		config: config,
	}

	// 初始化数据库（如果配置了）
	if config.DB.DBPath != "" {
		db, err := database.NewWorkerDB(config.DB)
		if err != nil {
			log.Printf("Failed to initialize worker database: %v", err)
		} else {
			// 初始化数据库 schema
			if err := db.InitSchema(); err != nil {
				log.Printf("Failed to initialize worker database schema: %v", err)
			}

			// 🎯 只设置Worker的数据库实例
			manager.worker.db = db
			manager.worker.deployRecordWRepo = database.NewDeployRecordWRepository(db)
		}
	}

	return manager
}

// NewWorkerManagerWithWorker 使用已有的 Worker 实例创建 WorkerManager
func NewWorkerManagerWithWorker(workerInstance *Worker) *WorkerManager {
	manager := &WorkerManager{
		worker: workerInstance,
		config: workerInstance.config,
	}

	// 复用Worker的数据库连接，或在需要时创建新的
	if workerInstance.db == nil && workerInstance.config.DB.DBPath != "" {
		// 只有在Worker没有数据库连接时才创建新的
		db, err := database.NewWorkerDB(workerInstance.config.DB)
		if err != nil {
			log.Printf("Failed to initialize worker database: %v", err)
		} else {
			// 🎯 只设置Worker的数据库实例
			manager.worker.db = db
			manager.worker.deployRecordWRepo = database.NewDeployRecordWRepository(db)
		}
	}

	return manager
}

// DeployContainer 部署容器
func (m *WorkerManager) DeployContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.DeployContainer(ctx, req)
}

// StopContainer 停止容器
func (m *WorkerManager) StopContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.StopContainer(ctx, req)
}

// RestartContainer 重启容器
func (m *WorkerManager) RestartContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.RestartContainer(ctx, req)
}

// HealthCheck 获取当前 worker 的健康状态
func (m *WorkerManager) HealthCheck(ctx context.Context) (*models.WorkerResponse, error) {
	return m.worker.HealthCheck(ctx)
}

// GetDeployStatus 获取容器部署状态
func (m *WorkerManager) GetDeployStatus(ctx context.Context, serviceID, nodeIP string) (*models.ContainerStatus, error) {
	return m.worker.GetDeployStatus(ctx, serviceID, nodeIP)
}

// SetStatus 设置当前 worker 的状态
func (m *WorkerManager) SetStatus(status WorkerStatus) {
	SetWorkerStatus(status)
}
