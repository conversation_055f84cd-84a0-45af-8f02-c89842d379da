package worker

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v3"

	"github.com/zero-ops/service-system/internal/database"
)

// WorkerStatus 表示 worker 的状态
type WorkerStatus string

const (
	// StatusAvailable 表示 worker 可用
	StatusAvailable WorkerStatus = "AVAILABLE"
	// StatusFreeze 表示 worker 冻结
	StatusFreeze WorkerStatus = "FREEZE"
)

// 全局状态变量，默认为冻结状态
var (
	workerStatus     WorkerStatus = StatusFreeze
	workerStatusLock sync.RWMutex
)

// Config 保存 worker 的配置
type Config struct {
	// worker 特定的配置项
	LogDir string // 日志目录

	// 数据库配置
	DB database.WorkerDBConfig

	// SSH配置
	SSHDefaultUser    string // SSH默认用户名
	SSHDefaultPort    int    // SSH默认端口
	SSHTimeout        int    // SSH连接超时时间(秒)
	SSHDefaultKeyPath string // SSH默认密钥路径

	// 🆕 通知系统配置
	Notification NotificationConfig `yaml:"notification" json:"notification"`
}

// NotificationConfig Worker层通知系统配置
type NotificationConfig struct {
	Feishu   FeishuNotificationConfig   `yaml:"feishu" json:"feishu"`
	WeChat   WeChatNotificationConfig   `yaml:"wechat" json:"wechat"`
	DingTalk DingTalkNotificationConfig `yaml:"dingtalk" json:"dingtalk"`
	Email    EmailNotificationConfig    `yaml:"email" json:"email"`
}

// FeishuNotificationConfig 飞书通知配置
type FeishuNotificationConfig struct {
	Enabled    bool              `yaml:"enabled" json:"enabled"`
	WebhookURL string            `yaml:"webhook_url" json:"webhook_url"`
	UserMap    map[string]string `yaml:"user_map" json:"user_map"`
}

// WeChatNotificationConfig 微信通知配置
type WeChatNotificationConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	AppID      string `yaml:"app_id" json:"app_id"`
	Secret     string `yaml:"secret" json:"secret"`
}

// DingTalkNotificationConfig 钉钉通知配置
type DingTalkNotificationConfig struct {
	Enabled     bool   `yaml:"enabled" json:"enabled"`
	WebhookURL  string `yaml:"webhook_url" json:"webhook_url"`
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Enabled     bool     `yaml:"enabled" json:"enabled"`
	SMTPHost    string   `yaml:"smtp_host" json:"smtp_host"`
	SMTPPort    int      `yaml:"smtp_port" json:"smtp_port"`
	Username    string   `yaml:"username" json:"username"`
	Password    string   `yaml:"password" json:"password"`
	FromName    string   `yaml:"from_name" json:"from_name"`
	ToAddresses []string `yaml:"to_addresses" json:"to_addresses"`
}

// DefaultConfig 返回默认的 worker 配置
func DefaultConfig() Config {
	return Config{
		LogDir:            "log",
		DB:                database.DefaultWorkerDBConfig(),
		SSHDefaultUser:    "root",
		SSHDefaultPort:    22,
		SSHTimeout:        10,
		SSHDefaultKeyPath: "dbdata/rsa.pem",
		// 默认通知配置
		Notification: NotificationConfig{
			Feishu: FeishuNotificationConfig{
				Enabled:    false,
				WebhookURL: "",
				UserMap:    make(map[string]string),
			},
			WeChat: WeChatNotificationConfig{
				Enabled:    false,
				WebhookURL: "",
				AppID:      "",
				Secret:     "",
			},
			DingTalk: DingTalkNotificationConfig{
				Enabled:     false,
				WebhookURL:  "",
				AccessToken: "",
			},
			Email: EmailNotificationConfig{
				Enabled:     false,
				SMTPHost:    "",
				SMTPPort:    587,
				Username:    "",
				Password:    "",
				FromName:    "Zero Ops Worker",
				ToAddresses: []string{},
			},
		},
	}
}

// LoadConfig 加载 Worker 配置的主函数
// 优先级：1. config/ 文件夹内的配置文件 2. 默认配置
func LoadConfig() *Config {
	// 尝试加载配置文件
	configPath := getWorkerConfigFilePath()

	fmt.Println("======worker configfile path=====", configPath)

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Println("======worker configfile path not exist=====")
		// 配置文件不存在，使用默认配置
		config := DefaultConfig()
		return &config
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		// 读取失败，使用默认配置
		fmt.Printf("Failed to read worker config file: %v\n", err)
		config := DefaultConfig()
		return &config
	}

	// 解析 YAML
	config := DefaultConfig() // 从默认配置开始，确保所有字段都有默认值
	if err := yaml.Unmarshal(data, &config); err != nil {
		// 解析失败，使用默认配置
		fmt.Printf("Failed to parse worker config file: %v\n", err)
		config := DefaultConfig()
		return &config
	}

	return &config
}

// getWorkerConfigFilePath 获取 Worker 配置文件路径
// 使用 config-worker-xxx.yaml 命名规范
func getWorkerConfigFilePath() string {
	env := os.Getenv("ENV")
	switch env {
	case "dev", "development":
		return "config/config-worker-dev.yaml"
	case "prod", "production":
		return "config/config-worker-prod.yaml"
	case "staging", "test":
		return "config/config-worker-staging.yaml"
	default:
		return "config/config-worker.yaml"
	}
}

// GetWorkerStatus 获取当前 worker 的状态
func GetWorkerStatus() WorkerStatus {
	workerStatusLock.RLock()
	defer workerStatusLock.RUnlock()
	return workerStatus
}

// SetWorkerStatus 设置当前 worker 的状态
func SetWorkerStatus(status WorkerStatus) {
	workerStatusLock.Lock()
	defer workerStatusLock.Unlock()
	workerStatus = status
}
