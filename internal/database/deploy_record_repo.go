package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// parseFloat 安全地将字符串解析为浮点数，如果解析失败则返回默认值
func parseFloat(s string, defaultValue float64) (float64, error) {
	// 清理字符串，移除可能导致解析错误的字符
	s = strings.TrimSpace(s)

	// 尝试解析
	val, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return defaultValue, err
	}

	return val, nil
}

// DeployRecordRepository handles database operations for deploy records
type DeployRecordRepository struct {
	db *ServiceDB
}

// NewDeployRecordRepository creates a new deploy record repository
func NewDeployRecordRepository(db *ServiceDB) *DeployRecordRepository {
	return &DeployRecordRepository{db: db}
}

// Create inserts a new deploy record into the database
func (r *DeployRecordRepository) Create(record *models.DeployRecord) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 获取当前东八区时间
	now := utils.GetCSTTimeString()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(record.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert customer_envs to JSON
	customerEnvsJSON, err := json.Marshal(record.CustomerEnvs)
	if err != nil {
		return fmt.Errorf("failed to marshal customer_envs: %w", err)
	}

	result, err := r.db.Exec(`
INSERT INTO deploy_record (
service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, host_ip, created_at, updated_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, record.ServiceID, record.Name, record.ImageName, record.ServiceType, record.DomainPrefix, record.DomainSuffix,
		record.Expiration, record.DurationSeconds, record.Status, string(labelsJSON), string(customerEnvsJSON), record.Remark, record.WorkerID, record.NodeIP, record.HostIP, now, now,
		record.ApiReplica, record.ApiCpu, record.ApiMemory, record.AutoReplica, record.AutoCpu, record.AutoMemory)

	if err != nil {
		return fmt.Errorf("failed to create deploy record: %w", err)
	}

	// Get the auto-generated ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}
	record.ID = id

	return nil
}

// GetByID retrieves a deploy record by ID
func (r *DeployRecordRepository) GetByID(id string) (models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var record models.DeployRecord
	var workerID, nodeIP, hostIP sql.NullString

	// First try to find by service_id
	var labelsJSON, customerEnvsJSON string
	var apiCpuStr, autoCpuStr string // 使用字符串类型来接收可能有问题的浮点数字段

	var visitedAt sql.NullString
	err := r.db.QueryRow(`
SELECT id, service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, host_ip, created_at, updated_at, visited_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
FROM deploy_record
WHERE service_id = ?
`, id).Scan(
		&record.ID, &record.ServiceID, &record.Name, &record.ImageName, &record.ServiceType, &record.DomainPrefix, &record.DomainSuffix,
		&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &workerID, &nodeIP, &hostIP,
		&record.CreatedAt, &record.UpdatedAt, &visitedAt,
		&record.ApiReplica, &apiCpuStr, &record.ApiMemory, &record.AutoReplica, &autoCpuStr, &record.AutoMemory,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// Try to find by numeric ID
			if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
				var visitedAtBackup sql.NullString
				err = r.db.QueryRow(`
SELECT id, service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, host_ip, created_at, updated_at, visited_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
FROM deploy_record
WHERE id = ?
`, numID).Scan(
					&record.ID, &record.ServiceID, &record.Name, &record.ImageName, &record.ServiceType, &record.DomainPrefix, &record.DomainSuffix,
					&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &workerID, &nodeIP, &hostIP,
					&record.CreatedAt, &record.UpdatedAt, &visitedAtBackup,
					&record.ApiReplica, &apiCpuStr, &record.ApiMemory, &record.AutoReplica, &autoCpuStr, &record.AutoMemory,
				)

				if visitedAtBackup.Valid {
					record.VisitedAt = visitedAtBackup.String
				}
				if err != nil {
					if err == sql.ErrNoRows {
						return models.DeployRecord{}, fmt.Errorf("deploy record not found: %s", id)
					}
					return models.DeployRecord{}, fmt.Errorf("failed to get deploy record: %w", err)
				}
			} else {
				return models.DeployRecord{}, fmt.Errorf("deploy record not found: %s", id)
			}
		} else {
			return models.DeployRecord{}, fmt.Errorf("failed to get deploy record: %w", err)
		}
	}

	// 安全地解析 api_cpu 字段
	apiCpu, err := parseFloat(apiCpuStr, 0.8) // 默认值为 0.8
	if err != nil {
		log.Printf("Warning: Invalid api_cpu value '%s' for record %s, using default: %v", apiCpuStr, record.ServiceID, err)
	}
	record.ApiCpu = apiCpu

	// 安全地解析 auto_cpu 字段
	autoCpu, err := parseFloat(autoCpuStr, 0.8) // 默认值为 0.8
	if err != nil {
		log.Printf("Warning: Invalid auto_cpu value '%s' for record %s, using default: %v", autoCpuStr, record.ServiceID, err)
	}
	record.AutoCpu = autoCpu

	if workerID.Valid {
		record.WorkerID = workerID.String
	}

	if nodeIP.Valid {
		record.NodeIP = nodeIP.String
	}

	if hostIP.Valid {
		record.HostIP = hostIP.String
	}

	if visitedAt.Valid {
		record.VisitedAt = visitedAt.String
	}

	// Parse labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// Parse customer_envs JSON
	if customerEnvsJSON != "" {
		if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
		}
	}

	return record, nil
}

// GetAll retrieves all deploy records with pagination and filtering
func (r *DeployRecordRepository) GetAll(page, pageSize int, serviceIDs []string) ([]models.DeployRecord, int, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建基础查询
	baseQuery := `
SELECT id, service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, host_ip, created_at, updated_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
FROM deploy_record
`
	// 构建 WHERE 子句
	whereClause := ""
	var args []interface{}

	if len(serviceIDs) > 0 {
		placeholders := make([]string, len(serviceIDs))
		for i, id := range serviceIDs {
			placeholders[i] = "?"
			args = append(args, id)
		}
		whereClause = fmt.Sprintf("WHERE service_id IN (%s)", strings.Join(placeholders, ","))
	}

	// 构建计数查询
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM deploy_record %s", whereClause)

	// 执行计数查询
	var total int
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count deploy records: %w", err)
	}

	// 添加排序和分页
	query := fmt.Sprintf("%s %s ORDER BY created_at DESC LIMIT ? OFFSET ?", baseQuery, whereClause)

	// 计算分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 添加分页参数
	args = append(args, pageSize, offset)

	// 执行查询
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query deploy records: %w", err)
	}
	defer rows.Close()

	var records []models.DeployRecord
	for rows.Next() {
		var record models.DeployRecord
		var workerID, nodeIP, hostIP sql.NullString
		var labelsJSON, customerEnvsJSON string
		var apiCpuStr, autoCpuStr string // 使用字符串类型来接收可能有问题的浮点数字段

		if err := rows.Scan(
			&record.ID, &record.ServiceID, &record.Name, &record.ImageName, &record.ServiceType, &record.DomainPrefix, &record.DomainSuffix,
			&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &workerID, &nodeIP, &hostIP,
			&record.CreatedAt, &record.UpdatedAt,
			&record.ApiReplica, &apiCpuStr, &record.ApiMemory, &record.AutoReplica, &autoCpuStr, &record.AutoMemory,
		); err != nil {
			return nil, 0, fmt.Errorf("failed to scan deploy record: %w", err)
		}

		// 安全地解析 api_cpu 字段
		apiCpu, err := parseFloat(apiCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid api_cpu value '%s' for record %s, using default: %v", apiCpuStr, record.ServiceID, err)
		}
		record.ApiCpu = apiCpu

		// 安全地解析 auto_cpu 字段
		autoCpu, err := parseFloat(autoCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid auto_cpu value '%s' for record %s, using default: %v", autoCpuStr, record.ServiceID, err)
		}
		record.AutoCpu = autoCpu

		if workerID.Valid {
			record.WorkerID = workerID.String
		}

		if nodeIP.Valid {
			record.NodeIP = nodeIP.String
		}

		if hostIP.Valid {
			record.HostIP = hostIP.String
		}

		// Parse labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
				return nil, 0, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// Parse customer_envs JSON
		if customerEnvsJSON != "" {
			if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
				return nil, 0, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
			}
		}

		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating deploy records: %w", err)
	}

	return records, total, nil
}

// GetAllLegacy retrieves all deploy records (legacy method without pagination)
func (r *DeployRecordRepository) GetAllLegacy() ([]models.DeployRecord, error) {
	records, _, err := r.GetAll(0, 0, nil)
	return records, err
}

// GetRecordsByStatus retrieves all deploy records with the specified statuses
func (r *DeployRecordRepository) GetRecordsByStatus(statuses []string) ([]models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建基础查询
	baseQuery := `
SELECT id, service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, host_ip, created_at, updated_at, visited_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
FROM deploy_record
`
	// 构建 WHERE 子句
	whereClause := ""
	var args []interface{}

	if len(statuses) > 0 {
		placeholders := make([]string, len(statuses))
		for i, status := range statuses {
			placeholders[i] = "?"
			args = append(args, status)
		}
		whereClause = fmt.Sprintf("WHERE status IN (%s)", strings.Join(placeholders, ","))
	}

	// 添加排序 - 按 updated_at 升序排序，实现先进先出原则
	query := fmt.Sprintf("%s %s ORDER BY updated_at ASC", baseQuery, whereClause)

	// 执行查询
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query deploy records by status: %w", err)
	}
	defer rows.Close()

	var records []models.DeployRecord
	for rows.Next() {
		var record models.DeployRecord
		var workerID, nodeIP, hostIP, visitedAt sql.NullString
		var labelsJSON, customerEnvsJSON string
		var apiCpuStr, autoCpuStr string // 使用字符串类型来接收可能有问题的浮点数字段

		if err := rows.Scan(
			&record.ID, &record.ServiceID, &record.Name, &record.ImageName, &record.ServiceType, &record.DomainPrefix, &record.DomainSuffix,
			&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &workerID, &nodeIP, &hostIP,
			&record.CreatedAt, &record.UpdatedAt, &visitedAt,
			&record.ApiReplica, &apiCpuStr, &record.ApiMemory, &record.AutoReplica, &autoCpuStr, &record.AutoMemory,
		); err != nil {
			return nil, fmt.Errorf("failed to scan deploy record: %w", err)
		}

		// 安全地解析 api_cpu 字段
		apiCpu, err := parseFloat(apiCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid api_cpu value '%s' for record %s, using default: %v", apiCpuStr, record.ServiceID, err)
		}
		record.ApiCpu = apiCpu

		// 安全地解析 auto_cpu 字段
		autoCpu, err := parseFloat(autoCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid auto_cpu value '%s' for record %s, using default: %v", autoCpuStr, record.ServiceID, err)
		}
		record.AutoCpu = autoCpu

		if workerID.Valid {
			record.WorkerID = workerID.String
		}

		if nodeIP.Valid {
			record.NodeIP = nodeIP.String
		}

		if hostIP.Valid {
			record.HostIP = hostIP.String
		}

		if visitedAt.Valid {
			record.VisitedAt = visitedAt.String
		}

		// Parse labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// Parse customer_envs JSON
		if customerEnvsJSON != "" {
			if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
			}
		}

		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating deploy records: %w", err)
	}

	return records, nil
}

// Update updates a deploy record's information
func (r *DeployRecordRepository) Update(record models.DeployRecord) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 获取当前东八区时间
	now := utils.GetCSTTimeString()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(record.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert customer_envs to JSON
	customerEnvsJSON, err := json.Marshal(record.CustomerEnvs)
	if err != nil {
		return fmt.Errorf("failed to marshal customer_envs: %w", err)
	}

	// 处理 visited_at 字段，如果为空则设为 NULL
	var visitedAtParam interface{}
	if record.VisitedAt == "" {
		visitedAtParam = nil
	} else {
		visitedAtParam = record.VisitedAt
	}

	_, err = r.db.Exec(`
UPDATE deploy_record
SET name = ?, image_name = ?, service_type = ?, domain_prefix = ?, domain_suffix = ?,
expiration = ?, duration_seconds = ?, status = ?, labels = ?, customer_envs = ?, remark = ?, worker_id = ?, node_ip = ?, host_ip = ?, updated_at = ?, visited_at = ?,
api_replica = ?, api_cpu = ?, api_memory = ?, auto_replica = ?, auto_cpu = ?, auto_memory = ?
WHERE id = ?
`, record.Name, record.ImageName, record.ServiceType, record.DomainPrefix, record.DomainSuffix,
		record.Expiration, record.DurationSeconds, record.Status, string(labelsJSON), string(customerEnvsJSON), record.Remark, record.WorkerID, record.NodeIP, record.HostIP, now, visitedAtParam,
		record.ApiReplica, record.ApiCpu, record.ApiMemory, record.AutoReplica, record.AutoCpu, record.AutoMemory,
		record.ID)

	if err != nil {
		return fmt.Errorf("failed to update deploy record: %w", err)
	}

	return nil
}

// UpdateStatus updates a deploy record's status
func (r *DeployRecordRepository) UpdateStatus(id string, status string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 获取当前东八区时间
	now := utils.GetCSTTimeString()

	// First try to update by service_id
	result, err := r.db.Exec(`
UPDATE deploy_record
SET status = ?, updated_at = ?
WHERE service_id = ?
`, status, now, id)

	if err != nil {
		return fmt.Errorf("failed to update deploy record status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// If no rows were affected, try to update by numeric ID
	if rowsAffected == 0 {
		if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
			_, err = r.db.Exec(`
UPDATE deploy_record
SET status = ?, updated_at = ?
WHERE id = ?
`, status, now, numID)

			if err != nil {
				return fmt.Errorf("failed to update deploy record status by ID: %w", err)
			}
		} else {
			return fmt.Errorf("deploy record not found: %s", id)
		}
	}

	return nil
}

// Delete removes a deploy record from the database
func (r *DeployRecordRepository) Delete(id string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// First try to delete by service_id
	result, err := r.db.Exec("DELETE FROM deploy_record WHERE service_id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete deploy record: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// If no rows were affected, try to delete by numeric ID
	if rowsAffected == 0 {
		if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
			_, err = r.db.Exec("DELETE FROM deploy_record WHERE id = ?", numID)
			if err != nil {
				return fmt.Errorf("failed to delete deploy record by ID: %w", err)
			}
		} else {
			return fmt.Errorf("deploy record not found: %s", id)
		}
	}

	return nil
}

// GetNextQueueingRecord retrieves the oldest record(s) with status QUEUEING
// count: optional parameter, if not provided or <= 0, defaults to 1
func (r *DeployRecordRepository) GetNextQueueingRecord(count ...int) ([]models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 处理可选参数，默认为1
	limit := 1
	if len(count) > 0 && count[0] > 0 {
		limit = count[0]
	}

	// 查询记录
	rows, err := r.db.Query(`
SELECT id, service_id, name, image_name, service_type, domain_prefix, domain_suffix,
expiration, duration_seconds, status, labels, customer_envs, remark, worker_id, node_ip, created_at, updated_at, visited_at,
api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory
FROM deploy_record
WHERE status = 'QUEUEING'
ORDER BY
    CASE
        WHEN service_type != 'TRIAL' THEN 0
        ELSE 1
    END ASC,
    updated_at ASC
LIMIT ?
`, limit)

	if err != nil {
		return nil, fmt.Errorf("failed to query next queueing deploy record: %w", err)
	}
	defer rows.Close()

	var records []models.DeployRecord
	for rows.Next() {
		var record models.DeployRecord
		var workerID, nodeIP, visitedAt sql.NullString
		var labelsJSON, customerEnvsJSON string
		var apiCpuStr, autoCpuStr string // 使用字符串类型来接收可能有问题的浮点数字段

		// 扫描到临时变量中
		err = rows.Scan(
			&record.ID, &record.ServiceID, &record.Name, &record.ImageName, &record.ServiceType, &record.DomainPrefix, &record.DomainSuffix,
			&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &workerID, &nodeIP,
			&record.CreatedAt, &record.UpdatedAt, &visitedAt,
			&record.ApiReplica, &apiCpuStr, &record.ApiMemory, &record.AutoReplica, &autoCpuStr, &record.AutoMemory,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan next queueing deploy record: %w", err)
		}

		// 安全地解析 api_cpu 字段
		apiCpu, err := parseFloat(apiCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid api_cpu value '%s' for record %s, using default: %v", apiCpuStr, record.ServiceID, err)
		}
		record.ApiCpu = apiCpu

		// 安全地解析 auto_cpu 字段
		autoCpu, err := parseFloat(autoCpuStr, 0.8) // 默认值为 0.8
		if err != nil {
			log.Printf("Warning: Invalid auto_cpu value '%s' for record %s, using default: %v", autoCpuStr, record.ServiceID, err)
		}
		record.AutoCpu = autoCpu

		if workerID.Valid {
			record.WorkerID = workerID.String
		}

		if nodeIP.Valid {
			record.NodeIP = nodeIP.String
		}

		if visitedAt.Valid {
			record.VisitedAt = visitedAt.String
		}

		// Parse labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// Parse customer_envs JSON
		if customerEnvsJSON != "" {
			if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
			}
		}

		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating queueing deploy records: %w", err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("no queueing deploy records found")
	}

	return records, nil
}

// GetQueuePosition 使用窗口函数计算指定服务在同类型服务中的排队位置
// 只考虑状态为 QUEUEING 的记录，按 created_at 排序（先入先出）
// 返回完整的 QueueInfo 对象，包含 position, total_in_queue, service_type
func (r *DeployRecordRepository) GetQueuePosition(serviceID string) (*models.QueueInfo, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 检查服务是否存在且在排队状态
	var exists bool
	var currentStatus string
	err := r.db.QueryRow(`
		SELECT
			EXISTS(
				SELECT 1 FROM deploy_record
				WHERE service_id = ? AND status = 'QUEUEING'
			),
			COALESCE(
				(SELECT status FROM deploy_record WHERE service_id = ?),
				''
			)
	`, serviceID, serviceID).Scan(&exists, &currentStatus)

	if err != nil {
		return nil, fmt.Errorf("failed to check service: %w", err)
	}

	if !exists {
		if currentStatus == "" {
			return nil, fmt.Errorf("service not found")
		}
		// 服务存在但不在排队中
		return nil, fmt.Errorf("service not in queue (current status: %s)", currentStatus)
	}

	// 使用窗口函数计算排队位置
	// 只考虑 QUEUEING 状态的记录，按 created_at 排序
	var queuePosition, totalInQueue int
	var serviceType string
	err = r.db.QueryRow(`
		WITH ranked_services AS (
			SELECT
				service_id,
				service_type,
				ROW_NUMBER() OVER (
					PARTITION BY service_type
					ORDER BY created_at ASC
				) as queue_position,
				COUNT(*) OVER (PARTITION BY service_type) as total_in_queue
			FROM deploy_record
			WHERE status = 'QUEUEING'
		)
		SELECT service_type, queue_position, total_in_queue
		FROM ranked_services
		WHERE service_id = ?
	`, serviceID).Scan(&serviceType, &queuePosition, &totalInQueue)

	if err != nil {
		return nil, fmt.Errorf("failed to calculate queue position: %w", err)
	}

	return &models.QueueInfo{
		Position:     queuePosition,
		TotalInQueue: totalInQueue,
		ServiceType:  serviceType,
	}, nil
}

// GetBatchQueuePositions 批量查询多个服务的排队位置
// 使用窗口函数一次性查询多个服务，只考虑状态为 QUEUEING 的记录
// serviceIDs: 要查询的服务ID列表（最多10个）
// 返回: map[serviceID]*QueueInfo，只包含在排队中的服务
func (r *DeployRecordRepository) GetBatchQueuePositions(serviceIDs []string) (map[string]*models.QueueInfo, error) {
	if len(serviceIDs) == 0 {
		return make(map[string]*models.QueueInfo), nil
	}

	// 限制查询数量
	const MaxBatchSize = 10
	if len(serviceIDs) > MaxBatchSize {
		return nil, fmt.Errorf("too many services for batch queue position query: %d > %d", len(serviceIDs), MaxBatchSize)
	}

	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建IN子句的占位符
	placeholders := make([]string, len(serviceIDs))
	args := make([]interface{}, len(serviceIDs))
	for i, serviceID := range serviceIDs {
		placeholders[i] = "?"
		args[i] = serviceID
	}
	inClause := strings.Join(placeholders, ",")

	// 使用窗口函数批量查询排队位置
	query := fmt.Sprintf(`
		WITH ranked_services AS (
			SELECT
				service_id,
				service_type,
				ROW_NUMBER() OVER (
					PARTITION BY service_type
					ORDER BY created_at ASC
				) as queue_position,
				COUNT(*) OVER (PARTITION BY service_type) as total_in_queue
			FROM deploy_record
			WHERE status = 'QUEUEING'
		)
		SELECT service_id, service_type, queue_position, total_in_queue
		FROM ranked_services
		WHERE service_id IN (%s)
	`, inClause)

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute batch queue position query: %w", err)
	}
	defer rows.Close()

	// 解析查询结果
	queueInfos := make(map[string]*models.QueueInfo)
	for rows.Next() {
		var serviceID, serviceType string
		var position, totalInQueue int

		err := rows.Scan(&serviceID, &serviceType, &position, &totalInQueue)
		if err != nil {
			return nil, fmt.Errorf("failed to scan queue position result: %w", err)
		}

		queueInfos[serviceID] = &models.QueueInfo{
			Position:     position,
			TotalInQueue: totalInQueue,
			ServiceType:  serviceType,
		}
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating queue position results: %w", err)
	}

	return queueInfos, nil
}
