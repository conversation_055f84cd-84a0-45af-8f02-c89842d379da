package service

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// Service represents the management node that interacts with users
type Service struct {
	// Database repositories
	db                *database.ServiceDB
	workerRepo        *database.WorkerRepository
	deployRecordRepo  *database.DeployRecordRepository
	imageTypeRepo     *database.ImageTypeRepository
	resourceUsageRepo *database.ServiceResourceUsageRepository

	// OSS 管理器
	ossManager *OSSManager

	// 队列处理器状态
	queueProcessorMu      sync.Mutex
	queueProcessorRunning bool
	queueProcessorCancel  context.CancelFunc

	// Worker资源分配锁管理 - 避免并发更新冲突
	workerLocksMu sync.RWMutex           // 保护workerLocks map的并发访问
	workerLocks   map[string]*sync.Mutex // 每个worker一个锁，key为workerID

	// 通知管理器 - 公共字段，其他包可以直接访问
	NotificationManager *utils.NotificationManager
}

// NewService creates a new service instance
func NewService() *Service {
	config := DefaultConfig()
	return NewServiceWithNewConfig(config)
}

// NewServiceWithNewConfig creates a new service instance with the given configuration
func NewServiceWithNewConfig(config Config) *Service {
	// Create service instance
	svc := &Service{
		queueProcessorMu:      sync.Mutex{},
		queueProcessorRunning: false,
		queueProcessorCancel:  nil,
		workerLocksMu:         sync.RWMutex{},
		workerLocks:           make(map[string]*sync.Mutex),
		NotificationManager: utils.CreateNotificationManagerFromConfig(utils.NotificationConfig{
			Feishu: utils.FeishuNotificationConfig{
				Enabled:    config.Notification.Feishu.Enabled,
				WebhookURL: config.Notification.Feishu.WebhookURL,
				UserMap:    config.Notification.Feishu.UserMap,
			},
			WeChat: utils.WeChatNotificationConfig{
				Enabled:    config.Notification.WeChat.Enabled,
				WebhookURL: config.Notification.WeChat.WebhookURL,
				AppID:      config.Notification.WeChat.AppID,
				Secret:     config.Notification.WeChat.Secret,
			},
			DingTalk: utils.DingTalkNotificationConfig{
				Enabled:     config.Notification.DingTalk.Enabled,
				WebhookURL:  config.Notification.DingTalk.WebhookURL,
				AccessToken: config.Notification.DingTalk.AccessToken,
			},
			Email: utils.EmailNotificationConfig{
				Enabled:     config.Notification.Email.Enabled,
				SMTPHost:    config.Notification.Email.SMTPHost,
				SMTPPort:    config.Notification.Email.SMTPPort,
				Username:    config.Notification.Email.Username,
				Password:    config.Notification.Email.Password,
				FromName:    config.Notification.Email.FromName,
				ToAddresses: config.Notification.Email.ToAddresses,
			},
		}), // 基于配置初始化通知管理器
	}

	// 记录通知管理器初始化状态
	total, enabled := svc.NotificationManager.GetNotifierCount()
	log.Printf("Notification manager initialized: %d total notifiers, %d enabled", total, enabled)
	if enabled > 0 {
		log.Printf("Enabled notifiers: %v", svc.NotificationManager.GetEnabledNotifiers())
	}

	// Initialize database if we're in service mode
	dbConfig := database.ServiceDBConfig{
		DBPath: config.DBPath,
	}

	db, err := database.NewServiceDB(dbConfig)
	if err != nil {
		log.Printf("Failed to initialize database: %v", err)
	} else {
		// Initialize schema
		if err := db.InitSchema(); err != nil {
			log.Printf("Failed to initialize database schema: %v", err)
		}

		// Initialize repositories
		svc.db = db
		svc.workerRepo = database.NewWorkerRepository(db)
		svc.deployRecordRepo = database.NewDeployRecordRepository(db)
		svc.imageTypeRepo = database.NewImageTypeRepository(db)
		svc.resourceUsageRepo = database.NewServiceResourceUsageRepository(db)

		// Initialize OSS manager
		svc.ossManager = NewOSSManager(svc.deployRecordRepo)
		log.Println("OSS manager initialized")

		// Start the queue processor
		svc.StartQueueProcessor(context.Background())
		log.Println("Queue processor started")

		// Start the worker monitor
		svc.StartWorkerMonitor(context.Background())
		log.Println("Worker monitor started")

		// Resource monitoring configuration
		log.Println("Resource monitoring configuration:")
		log.Printf("- Hardware resource monitoring: enabled (15 minutes interval)")
		if utils.GetEnvBool("ENABLE_OSS_MONITORING", false) {
			log.Printf("- OSS resource monitoring: enabled (2 hours interval)")
		} else {
			log.Printf("- OSS resource monitoring: disabled")
		}
	}

	return svc
}

// RestartService handles service restart requests from users
func (s *Service) RestartService(ctx context.Context, service_id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(service_id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 对于重启操作，不需要检查 Worker 状态
	// 重启操作不会增加资源占用，只是重新启动现有容器，即使 Worker 处于 FREEZE 状态也可以安全执行
	log.Printf("Restart operation for service %s on worker %s (status: %s)", record.ServiceID, workerID, workerInfo.Status)

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		ServiceId: record.ServiceID, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 RestartContainer 函数
	workerResp, err := worker.RestartContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 PROCESSING
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// UpdateService handles service update requests from users
// 当前仅支持更新镜像名和镜像地址，其他参数保持不变
func (s *Service) UpdateService(ctx context.Context, req *models.UpdateServiceRequest) (*models.ServiceResponse, error) {
	log.Printf("Received update request for service: %s", req.ServiceInfo.ServiceID)

	// 🔒 使用队列处理器锁，确保与队列处理器互斥，避免并发冲突
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	// 1. 判断 service_id 对应的记录是否存在
	record, err := s.deployRecordRepo.GetByID(req.ServiceInfo.ServiceID)
	if err != nil {
		log.Printf("Service not found: %v", err)
		return &models.ServiceResponse{
			Code: 404,
			Msg:  fmt.Sprintf("Service not found: %v", err),
		}, fmt.Errorf("service not found: %w", err)
	}

	log.Printf("Found existing record for service %s with worker ID: %s, status: %s",
		req.ServiceInfo.ServiceID, record.WorkerID, record.Status)

	// 2. 判断 worker_id 的逻辑（优化后的逻辑）
	if record.WorkerID == "" {
		// worker_id 为空不是错误，说明还在排队中
		log.Printf("Service %s is still in queue (no worker assigned), will update record directly",
			req.ServiceInfo.ServiceID)
	} else {
		// worker_id 存在，验证 worker 是否有效
		_, err := s.workerRepo.GetByID(record.WorkerID)
		if err != nil {
			log.Printf("Worker %s data is invalid: %v", record.WorkerID, err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("Worker data is invalid: %v", err),
			}, fmt.Errorf("worker data is invalid: %w", err)
		}
		log.Printf("Service %s has assigned worker %s, will re-queue for update",
			req.ServiceInfo.ServiceID, record.WorkerID)
	}

	// 3. 验证状态转换的合理性
	allowedStatuses := []string{"QUEUEING", "PROCESSING", "RUNNING"}
	statusAllowed := false
	for _, status := range allowedStatuses {
		if record.Status == status {
			statusAllowed = true
			break
		}
	}

	if !statusAllowed {
		log.Printf("Current status %s does not allow update", record.Status)
		return &models.ServiceResponse{
			Code: 400,
			Msg:  fmt.Sprintf("Current status %s does not allow update", record.Status),
		}, fmt.Errorf("current status %s does not allow update", record.Status)
	}

	// 3.5. 验证资源配置（阶段 2.2）
	if err := s.validateResourceConfig(&req.ServiceInfo); err != nil {
		log.Printf("Resource configuration validation failed: %v", err)
		return &models.ServiceResponse{
			Code: 400,
			Msg:  fmt.Sprintf("资源配置验证失败: %v", err),
		}, err
	}

	// 4. 使用用户传递的数据更新 service 层 record 的记录
	// 设置 remark 字段为 UPDATE，status 字段为 QUEUEING
	originalStatus := record.Status
	record.Remark = "UPDATE"
	record.Status = "QUEUEING"

	// 🔄 镜像名称更新
	if req.ServiceInfo.ImageName != "" {
		record.ImageName = req.ServiceInfo.ImageName
		log.Printf("Updated image name to: %s", req.ServiceInfo.ImageName)
	}

	// ⏱️ 过期时间更新（阶段 2.1）
	if req.ServiceInfo.Expiration != "" {
		newExpiration, newDuration, err := s.validateAndCalculateExpiration(req.ServiceInfo.Expiration)
		if err != nil {
			log.Printf("Expiration validation failed: %v", err)
			return &models.ServiceResponse{
				Code: 400,
				Msg:  fmt.Sprintf("过期时间验证失败: %v", err),
			}, err
		}
		record.Expiration = newExpiration
		record.DurationSeconds = newDuration
		log.Printf("Updated expiration to: %s (duration: %d seconds)", newExpiration, newDuration)
	}

	// 🏷️ 标签更新（阶段 2.1）
	if len(req.ServiceInfo.Labels) > 0 {
		record.Labels = req.ServiceInfo.Labels
		log.Printf("Updated labels to: %v", req.ServiceInfo.Labels)
	}

	// 🌍 环境变量更新（阶段 2.1）
	if len(req.ServiceInfo.CustomerEnvs) > 0 {
		if err := s.validateEnvironmentVariables(req.ServiceInfo.CustomerEnvs); err != nil {
			log.Printf("Environment variables validation failed: %v", err)
			return &models.ServiceResponse{
				Code: 400,
				Msg:  fmt.Sprintf("环境变量验证失败: %v", err),
			}, err
		}
		record.CustomerEnvs = req.ServiceInfo.CustomerEnvs
		log.Printf("Updated environment variables: %d variables", len(req.ServiceInfo.CustomerEnvs))
	}

	// 🔧 API 资源配置更新（阶段 2.2）
	if req.ServiceInfo.ApiReplica != nil {
		record.ApiReplica = *req.ServiceInfo.ApiReplica
		log.Printf("Updated API replica to: %d", *req.ServiceInfo.ApiReplica)
	}
	if req.ServiceInfo.ApiCpu != nil {
		record.ApiCpu = *req.ServiceInfo.ApiCpu
		log.Printf("Updated API CPU to: %.2f", *req.ServiceInfo.ApiCpu)
	}
	if req.ServiceInfo.ApiMemory != nil {
		record.ApiMemory = *req.ServiceInfo.ApiMemory
		log.Printf("Updated API memory to: %d MB", *req.ServiceInfo.ApiMemory)
	}

	// 🔧 Auto 资源配置更新（阶段 2.2）
	if req.ServiceInfo.AutoReplica != nil {
		record.AutoReplica = *req.ServiceInfo.AutoReplica
		log.Printf("Updated Auto replica to: %d", *req.ServiceInfo.AutoReplica)
	}
	if req.ServiceInfo.AutoCpu != nil {
		record.AutoCpu = *req.ServiceInfo.AutoCpu
		log.Printf("Updated Auto CPU to: %.2f", *req.ServiceInfo.AutoCpu)
	}
	if req.ServiceInfo.AutoMemory != nil {
		record.AutoMemory = *req.ServiceInfo.AutoMemory
		log.Printf("Updated Auto memory to: %d MB", *req.ServiceInfo.AutoMemory)
	}

	// 更新数据库记录
	if err := s.deployRecordRepo.Update(record); err != nil {
		log.Printf("Failed to update deploy record: %v", err)
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("Failed to update record: %v", err),
		}, fmt.Errorf("failed to update record: %w", err)
	}

	// 构建更新摘要
	var updateSummary []string
	if req.ServiceInfo.ImageName != "" {
		updateSummary = append(updateSummary, "镜像")
	}
	if req.ServiceInfo.Expiration != "" {
		updateSummary = append(updateSummary, "过期时间")
	}
	if len(req.ServiceInfo.Labels) > 0 {
		updateSummary = append(updateSummary, "标签")
	}
	if len(req.ServiceInfo.CustomerEnvs) > 0 {
		updateSummary = append(updateSummary, "环境变量")
	}

	// 阶段 2.2：资源配置更新摘要
	if req.ServiceInfo.ApiReplica != nil || req.ServiceInfo.ApiCpu != nil || req.ServiceInfo.ApiMemory != nil {
		updateSummary = append(updateSummary, "API资源配置")
	}
	if req.ServiceInfo.AutoReplica != nil || req.ServiceInfo.AutoCpu != nil || req.ServiceInfo.AutoMemory != nil {
		updateSummary = append(updateSummary, "Auto资源配置")
	}

	log.Printf("Successfully updated service %s: %s -> QUEUEING, remark: UPDATE, fields: %v",
		req.ServiceInfo.ServiceID, originalStatus, updateSummary)

	return &models.ServiceResponse{
		Code: 200,
		Msg:  fmt.Sprintf("更新请求已加入队列，将更新: %s", strings.Join(updateSummary, "、")),
		Data: gin.H{
			"serviceId":      req.ServiceInfo.ServiceID,
			"status":         "QUEUEING",
			"remark":         "UPDATE",
			"previousStatus": originalStatus,
			"updatedFields":  updateSummary,
			"message":        "Especially attention: the service PORTS can't be changed",
		},
	}, nil
}

// validateAndCalculateExpiration 验证过期时间并计算持续时间（复用 CreateService 逻辑）
func (s *Service) validateAndCalculateExpiration(expiration string) (string, int64, error) {
	if expiration == "" {
		return "", 0, nil // 不更新
	}

	// 解析过期时间
	expirationTime, err := time.Parse(time.RFC3339, expiration)
	if err != nil {
		return "", 0, fmt.Errorf("过期时间格式错误，请使用 RFC3339 格式 (如: 2025-12-31T23:59:59Z): %w", err)
	}

	// 获取当前时间
	now := time.Now()
	// 计算持续时间（秒）
	durationSeconds := int64(expirationTime.Sub(now).Seconds())

	// 验证时间合理性
	if durationSeconds < 0 {
		return "", 0, fmt.Errorf("过期时间不能在过去")
	}

	const MaxDurationSeconds = 365 * 24 * 60 * 60 // 最大1年
	if durationSeconds > MaxDurationSeconds {
		return "", 0, fmt.Errorf("过期时间不能超过1年")
	}

	const MinDurationSeconds = 60 * 60 // 最小1小时
	if durationSeconds < MinDurationSeconds {
		return "", 0, fmt.Errorf("过期时间至少要在1小时后")
	}

	return expiration, durationSeconds, nil
}

// validateEnvironmentVariables 验证环境变量
func (s *Service) validateEnvironmentVariables(envs []string) error {
	if len(envs) > 50 {
		return fmt.Errorf("环境变量数量不能超过50个，当前: %d", len(envs))
	}

	for i, env := range envs {
		if env == "" {
			return fmt.Errorf("环境变量[%d]不能为空", i)
		}
		if len(env) > 1024 {
			return fmt.Errorf("环境变量[%d]长度不能超过1024字符", i)
		}
		// 可以添加更多格式验证，如 KEY=VALUE 格式
	}

	return nil
}

// validateResourceConfig 验证资源配置（阶段 2.2）
func (s *Service) validateResourceConfig(req *models.UpdateServiceInfo) error {
	// API CPU 验证
	if req.ApiCpu != nil {
		if *req.ApiCpu <= 0 || *req.ApiCpu > 16 {
			return fmt.Errorf("API CPU 必须在 0.1-16 之间，当前值: %.2f", *req.ApiCpu)
		}
	}

	// Auto CPU 验证
	if req.AutoCpu != nil {
		if *req.AutoCpu < 0 || *req.AutoCpu > 16 {
			return fmt.Errorf("AUTO CPU 必须在 0-16 之间，当前值: %.2f", *req.AutoCpu)
		}
	}

	// API 内存验证 (MB)
	if req.ApiMemory != nil {
		if *req.ApiMemory <= 0 || *req.ApiMemory > 32768 {
			return fmt.Errorf("API 内存必须在 128MB-32GB 之间，当前值: %dMB", *req.ApiMemory)
		}
	}

	// Auto 内存验证 (MB)
	if req.AutoMemory != nil {
		if *req.AutoMemory < 0 || *req.AutoMemory > 32768 {
			return fmt.Errorf("AUTO 内存必须在 0-32GB 之间，当前值: %dMB", *req.AutoMemory)
		}
	}

	// API 副本数验证
	if req.ApiReplica != nil {
		if *req.ApiReplica <= 0 || *req.ApiReplica > 10 {
			return fmt.Errorf("API 副本数必须在 1-10 之间，当前值: %d", *req.ApiReplica)
		}
	}

	// Auto 副本数验证
	if req.AutoReplica != nil {
		if *req.AutoReplica < 0 || *req.AutoReplica > 10 {
			return fmt.Errorf("AUTO 副本数必须在 0-10 之间，当前值: %d", *req.AutoReplica)
		}
	}

	return nil
}

// StopService stops a service on a worker node
func (s *Service) StopService(ctx context.Context, id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 对于停止操作，不需要检查 Worker 状态
	// 停止操作会释放资源，即使 Worker 处于 FREEZE 状态也可以安全执行，甚至有助于释放资源
	log.Printf("Stop operation for service %s on worker %s (status: %s)", id, workerID, workerInfo.Status)

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		HostIP:    workerInfo.HostIP,
		ServiceId: id, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 StopContainer 函数
	workerResp, err := worker.StopContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 STOPPED
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "STOPPED"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	} else {
		// 更新状态成功，发送通知
		s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelSuccess,
			Title:     "[S] 服务停止请求推送给worker",
			Content:   fmt.Sprintf("服务 %s 停止请求推送成功，record已更新为 STOPPED", record.ServiceID),
			ServiceID: record.ServiceID,
			Metadata:  nil,
			Timestamp: utils.GetCSTTimeString(),
		})
		log.Printf("Successfully updated deploy record status to STOPPED and sent notification for service %s", record.ServiceID)
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// GetWorkerList returns a list of all registered worker nodes with optional filtering
func (s *Service) GetWorkerList(ctx context.Context, filter *models.WorkerFilter) (*models.WorkerListResponse, error) {
	// 从数据库获取 worker 列表
	if s.workerRepo != nil {
		workers, err := s.workerRepo.GetAll(filter)
		if err != nil {
			log.Printf("Failed to get workers from database: %v", err)
			return &models.WorkerListResponse{
				Code:    500,
				Workers: nil,
				Msg:     fmt.Sprintf("无法从数据库获取 worker 列表: %v", err),
			}, fmt.Errorf("无法从数据库获取 worker 列表: %w", err)
		}

		return &models.WorkerListResponse{
			Code:    200,
			Workers: workers,
			Msg:     "Service: 获取Worker列表成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.WorkerListResponse{
		Code:    500,
		Workers: nil,
		Msg:     "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// RegisterWorker registers a new worker node with the service
func (s *Service) RegisterWorker(ctx context.Context, req *models.RegisterWorkerRequest) (*models.ServiceResponse, error) {
	// Generate a unique ID for the worker using ULID
	workerID, err := utils.GeneratePrefixedULID("worker")
	if err != nil {
		log.Printf("Failed to generate ULID for worker: %v", err)
		// Fallback to the old method if ULID generation fails
		workerID = fmt.Sprintf("worker%d", time.Now().Unix())
	}

	// Convert nodes array to Node struct array
	var nodes []models.Node
	nodes = append(nodes, req.Nodes...)

	// Create worker info
	workerInfo := models.WorkerInfo{
		WorkerID:     workerID,
		Name:         req.Name,
		ServerType:   req.ServerType,
		Labels:       req.Labels,
		Host:         req.Host,
		HostIP:       req.HostIP,
		DomainSuffix: req.DomainSuffix,
		Nodes:        nodes,
		Status:       "AVAILABLE",
	}

	// 存储到数据库
	if s.workerRepo != nil {
		if err := s.workerRepo.Create(workerInfo); err != nil {
			log.Printf("Failed to store worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法将 worker 保存到数据库: %v", err),
			}, fmt.Errorf("无法将 worker 保存到数据库: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: gin.H{"workerId": workerID},
		Msg:  "Service: Worker注册成功",
	}, nil
}

// RemoveWorker removes a worker node from the service
func (s *Service) RemoveWorker(ctx context.Context, worker_id string) (*models.ServiceResponse, error) {
	// 从数据库中删除 worker
	if s.workerRepo != nil {
		if err := s.workerRepo.Delete(worker_id); err != nil {
			log.Printf("Failed to remove worker from database: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker移除成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// UpdateWorker updates a worker node
func (s *Service) UpdateWorker(ctx context.Context, worker_id string, req *models.UpdateWorkerRequest) (*models.ServiceResponse, error) {
	// 从数据库获取 worker 信息
	if s.workerRepo != nil {
		worker, err := s.workerRepo.GetByID(worker_id)
		if err != nil {
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		// 添加调试日志
		log.Printf("UpdateWorker: worker_id=%s", worker_id)
		log.Printf("UpdateWorker: 更新前 worker.Nodes 数量=%d", len(worker.Nodes))
		if len(worker.Nodes) > 0 {
			for i, node := range worker.Nodes {
				log.Printf("UpdateWorker: 更新前 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
			}
		}
		log.Printf("UpdateWorker: 请求中 req.Nodes 数量=%d", len(req.Nodes))
		if len(req.Nodes) > 0 {
			for i, node := range req.Nodes {
				log.Printf("UpdateWorker: 请求中 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
			}
		}

		// 更新 worker 信息
		if req.Name != "" {
			worker.Name = req.Name
		}
		if req.ServerType != "" {
			worker.ServerType = req.ServerType
		}
		if req.Labels != nil {
			worker.Labels = req.Labels
		}
		if req.Host != "" {
			worker.Host = req.Host
		}
		if req.DomainSuffix != "" {
			worker.DomainSuffix = req.DomainSuffix
		}
		if req.Nodes != nil {
			// 创建一个map来存储现有节点，以IP为键
			existingNodes := make(map[string]models.Node)
			for _, node := range worker.Nodes {
				existingNodes[node.IP] = node
			}

			// 更新或添加请求中的节点
			for _, newNode := range req.Nodes {
				existingNodes[newNode.IP] = newNode
			}

			// 将map转换回切片
			worker.Nodes = []models.Node{}
			for _, node := range existingNodes {
				worker.Nodes = append(worker.Nodes, node)
			}

			log.Printf("UpdateWorker: 更新后 worker.Nodes 数量=%d", len(worker.Nodes))
			if len(worker.Nodes) > 0 {
				for i, node := range worker.Nodes {
					log.Printf("UpdateWorker: 更新后 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
				}
			}
		}
		if req.Status != "" {
			worker.Status = req.Status
		}

		// 更新数据库中的 worker 信息
		if err := s.workerRepo.Update(worker); err != nil {
			log.Printf("Failed to update worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法更新数据库中的 worker: %v", err),
			}, fmt.Errorf("无法更新数据库中的 worker: %w", err)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker更新成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// GetResourceStatus 获取业务资源使用状态（支持批量查询）
func (s *Service) GetResourceStatus(ctx context.Context, businessIDs []string) (*models.ServiceResponse, error) {
	log.Printf("开始获取业务资源使用状态: %v", businessIDs)

	// 检查输入参数
	if len(businessIDs) == 0 {
		return &models.ServiceResponse{
			Code: 400,
			Msg:  "business_ids 不能为空",
		}, fmt.Errorf("business_ids 不能为空")
	}

	// 检查资源使用统计仓库
	if s.resourceUsageRepo == nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "资源使用统计仓库不可用",
		}, fmt.Errorf("资源使用统计仓库不可用")
	}

	// 批量获取业务资源使用统计
	resourceUsageMap, err := s.resourceUsageRepo.GetByBusinessIDs(businessIDs)
	if err != nil {
		log.Printf("批量获取业务资源使用统计失败: %v", err)
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("批量获取业务资源使用统计失败: %v", err),
		}, fmt.Errorf("批量获取业务资源使用统计失败: %w", err)
	}

	// 处理查询结果
	var results []interface{}
	var notFoundBusinesses []string

	for _, businessID := range businessIDs {
		resourceUsage, found := resourceUsageMap[businessID]
		if !found {
			log.Printf("未找到业务资源使用统计: %s", businessID)
			notFoundBusinesses = append(notFoundBusinesses, businessID)
			continue
		}

		// 添加 business_id 字段到结果中
		resourceData := map[string]interface{}{
			"business_id":                businessID,
			"cpu_usage":                  resourceUsage.CPUUsage,
			"memory_usage":               resourceUsage.MemoryUsage,
			"disk_usage":                 resourceUsage.DiskUsage,
			"oss_disk_usage":             resourceUsage.OSSDiskUsage,
			"oss_network_traffic":        resourceUsage.OSSNetworkTraffic,
			"oss_network_traffic_by_day": resourceUsage.OSSNetworkTrafficByDay,
			"network_in_traffic":         resourceUsage.NetworkInTraffic,
			"network_out_traffic":        resourceUsage.NetworkOutTraffic,
			"network_traffic_updated_at": resourceUsage.NetworkTrafficUpdatedAt,
			"last_access_time":           resourceUsage.LastAccessTime,
			"metadata":                   resourceUsage.Metadata,
			"created_at":                 resourceUsage.CreatedAt,
			"updated_at":                 resourceUsage.UpdatedAt,
		}

		results = append(results, resourceData)
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"total":   len(businessIDs),
		"success": len(results),
		"results": results,
	}

	// 添加未找到的业务信息（如果有）
	if len(notFoundBusinesses) > 0 {
		responseData["not_found"] = notFoundBusinesses
	}

	// 构建响应消息
	var msg string
	if len(results) == len(businessIDs) {
		msg = "获取业务资源使用状态成功"
	} else if len(results) > 0 {
		msg = fmt.Sprintf("部分获取成功：成功 %d 个，未找到 %d 个", len(results), len(notFoundBusinesses))
	} else {
		msg = "未找到任何业务的资源使用统计"
	}

	log.Printf("批量获取业务资源使用状态完成: 总数=%d, 成功=%d, 未找到=%d",
		len(businessIDs), len(results), len(notFoundBusinesses))

	return &models.ServiceResponse{
		Code: 200,
		Data: responseData,
		Msg:  msg,
	}, nil
}

// GetServiceWaitingTime 获取服务排队等待时间
func (s *Service) GetServiceWaitingTime(ctx context.Context, serviceID string) (*models.ServiceResponse, error) {
	// 获取目标服务记录
	targetRecord, err := s.deployRecordRepo.GetByID(serviceID)
	if err != nil {
		return nil, fmt.Errorf("获取服务记录失败: %w", err)
	}

	// 检查服务状态
	if targetRecord.Status != "QUEUEING" {
		return &models.ServiceResponse{
			Code: 200,
			Msg:  fmt.Sprintf("服务不在排队中，当前状态为: %s", targetRecord.Status),
			Data: &models.WaitingTimeResponse{
				ServiceID:         serviceID,
				ServerType:        targetRecord.ServiceType,
				CurrentPosition:   0,
				EstimatedWaitTime: 0,
			},
		}, nil
	}

	// 计算排队等待时间
	waitingTime, err := s.calculateWaitingTime(ctx, targetRecord)
	if err != nil {
		return nil, fmt.Errorf("计算等待时间失败: %w", err)
	}

	return &models.ServiceResponse{
		Code: 200,
		Msg:  "获取排队等待时间成功",
		Data: waitingTime,
	}, nil
}

// calculateWaitingTime 计算服务的排队等待时间
// TODO: 优化代码，更具逻辑
func (s *Service) calculateWaitingTime(ctx context.Context, targetRecord models.DeployRecord) (*models.WaitingTimeResponse, error) {
	// 1. 获取同 server_type 的所有 QUEUEING 记录，按 updated_at 排序
	queueingRecords, err := s.getQueueingRecordsByServerType(targetRecord.ServiceType)
	if err != nil {
		return nil, fmt.Errorf("获取排队记录失败: %w", err)
	}

	// 2. 找到目标服务在队列中的位置
	targetPosition := -1
	for i, record := range queueingRecords {
		if record.ServiceID == targetRecord.ServiceID {
			targetPosition = i
			break
		}
	}

	if targetPosition == -1 {
		return nil, fmt.Errorf("目标服务不在排队队列中")
	}

	// 3. 获取可用的 Worker 节点资源
	availableResources, err := s.getAvailableResources(targetRecord.ServiceType)
	if err != nil {
		return nil, fmt.Errorf("获取可用资源失败: %w", err)
	}

	// 4. 模拟排队过程，计算等待时间
	result := s.simulateQueueProcessing(queueingRecords, targetPosition, availableResources, targetRecord)

	return result, nil
}

// getQueueingRecordsByServerType 获取指定服务类型的排队记录
func (s *Service) getQueueingRecordsByServerType(serverType string) ([]models.DeployRecord, error) {
	// 获取所有 QUEUEING 状态的记录
	allQueueingRecords, err := s.deployRecordRepo.GetRecordsByStatus([]string{"QUEUEING"})
	if err != nil {
		return nil, err
	}

	// 过滤出相同 server_type 的记录
	var filteredRecords []models.DeployRecord
	for _, record := range allQueueingRecords {
		if record.ServiceType == serverType {
			filteredRecords = append(filteredRecords, record)
		}
	}

	// 按 updated_at 排序（已经在 GetRecordsByStatus 中排序了）
	return filteredRecords, nil
}

// AvailableResourceInfo 表示可用资源信息
type AvailableResourceInfo struct {
	TotalCPU        float64
	TotalMemory     int
	AvailableCPU    float64
	AvailableMemory int
	WorkerNodes     []models.WorkerInfo
}

// getAvailableResources 获取指定服务类型的可用资源
func (s *Service) getAvailableResources(serverType string) (*AvailableResourceInfo, error) {
	// 获取所有可用的 Worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{})
	if err != nil {
		return nil, fmt.Errorf("获取 Worker 列表失败: %w", err)
	}

	// 过滤出匹配服务类型的 Worker
	var matchingWorkers []models.WorkerInfo
	for _, worker := range workers {
		if worker.ServerType == serverType && worker.Status == "AVAILABLE" {
			matchingWorkers = append(matchingWorkers, worker)
		}
	}

	if len(matchingWorkers) == 0 {
		return &AvailableResourceInfo{}, nil
	}

	// 计算总资源和可用资源
	var totalCPU, availableCPU float64
	var totalMemory, availableMemory int

	for _, worker := range matchingWorkers {
		for _, node := range worker.Nodes {
			// 总资源
			totalCPU += node.CPU
			totalMemory += node.Memory

			// 可用资源 = 总资源 × (1 - 使用率) - 预留资源 - 已分配资源
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			nodeCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			nodeMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			// 确保不为负数
			if nodeCPU > 0 {
				availableCPU += nodeCPU
			}
			if nodeMemory > 0 {
				availableMemory += nodeMemory
			}
		}
	}

	return &AvailableResourceInfo{
		TotalCPU:        totalCPU,
		TotalMemory:     totalMemory,
		AvailableCPU:    availableCPU,
		AvailableMemory: availableMemory,
		WorkerNodes:     matchingWorkers,
	}, nil
}

// simulateQueueProcessing 模拟排队处理过程
// 🚀 优化版本：按照node级别进行资源分配模拟，避免资源碎片问题
func (s *Service) simulateQueueProcessing(queueingRecords []models.DeployRecord, targetPosition int, availableResources *AvailableResourceInfo, targetRecord models.DeployRecord) *models.WaitingTimeResponse {
	// 常量定义
	const (
		deployTimeWithResource    = 6    // 有资源时的部署时间（分钟）
		deployTimeWithoutResource = 720  // 无资源时的等待时间（12小时=720分钟）
		edgeCaseWaitTime          = 7200 // 边界情况等待时间（120小时=7200分钟）
	)

	// 初始化响应
	response := &models.WaitingTimeResponse{
		ServiceID:         targetRecord.ServiceID,
		ServerType:        targetRecord.ServiceType,
		CurrentPosition:   targetPosition + 1, // 位置从1开始
		EstimatedWaitTime: edgeCaseWaitTime,   // 默认返回120小时
	}

	// 边界情况检查
	if targetPosition < 0 || targetPosition >= len(queueingRecords) {
		log.Printf("⚠️  目标位置无效: position=%d, queueLength=%d", targetPosition, len(queueingRecords))
		return response
	}

	if len(availableResources.WorkerNodes) == 0 {
		log.Printf("⚠️  没有可用的worker节点")
		return response
	}

	// 截取需要模拟的队列：从第一个到目标位置（包含目标记录）
	simulationQueue := queueingRecords[:targetPosition+1]
	log.Printf("🎯 开始模拟排队处理: 队列长度=%d, 目标位置=%d", len(simulationQueue), targetPosition)

	// 创建worker节点的虚拟副本，用于模拟资源分配
	virtualWorkers := s.createVirtualWorkers(availableResources.WorkerNodes)

	// 模拟处理队列中的每个记录
	resourceInsufficientFromIndex := -1
	for i, record := range simulationQueue {
		log.Printf("📋 处理队列位置 %d: 服务 %s (类型: %s)", i, record.ServiceID, record.ServiceType)

		// 计算资源需求
		cpuNeed := record.ApiCpu * float64(record.ApiReplica)
		memoryNeed := record.ApiMemory * record.ApiReplica

		log.Printf("  💾 资源需求: CPU=%.2f cores (%.2f × %d), Memory=%d MB (%d × %d)",
			cpuNeed, record.ApiCpu, record.ApiReplica, memoryNeed, record.ApiMemory, record.ApiReplica)

		// 尝试在虚拟worker中分配资源
		if s.tryAllocateResourceInVirtualWorkers(virtualWorkers, record.ServiceType, cpuNeed, memoryNeed) {
			log.Printf("  ✅ 资源分配成功")
		} else {
			log.Printf("  ❌ 资源分配失败，从位置 %d 开始资源不足", i)
			resourceInsufficientFromIndex = i
			break
		}
	}

	// 计算总等待时间
	totalWaitTime := s.calculateTotalWaitTime(len(simulationQueue), resourceInsufficientFromIndex, deployTimeWithResource, deployTimeWithoutResource)

	response.EstimatedWaitTime = totalWaitTime
	log.Printf("🎉 排队模拟完成: 总等待时间=%d分钟 (%.1f小时)", totalWaitTime, float64(totalWaitTime)/60)

	return response
}

// createVirtualWorkers 创建worker节点的虚拟副本，用于模拟资源分配
// 返回的虚拟worker可以在内存中修改，不会影响原始数据
func (s *Service) createVirtualWorkers(workers []models.WorkerInfo) []models.WorkerInfo {
	virtualWorkers := make([]models.WorkerInfo, len(workers))

	for i, worker := range workers {
		// 深拷贝worker信息
		virtualWorkers[i] = models.WorkerInfo{
			ID:           worker.ID,
			WorkerID:     worker.WorkerID,
			Name:         worker.Name,
			ServerType:   worker.ServerType,
			Labels:       worker.Labels,
			Host:         worker.Host,
			HostIP:       worker.HostIP,
			DomainSuffix: worker.DomainSuffix,
			Status:       worker.Status,
			CreatedAt:    worker.CreatedAt,
			UpdatedAt:    worker.UpdatedAt,
			Nodes:        make([]models.Node, len(worker.Nodes)),
		}

		// 深拷贝nodes信息
		for j, node := range worker.Nodes {
			virtualWorkers[i].Nodes[j] = models.Node{
				IP:                node.IP,
				Instance:          node.Instance,
				Memory:            node.Memory,
				Memory_use_rate:   node.Memory_use_rate,
				CPU:               node.CPU,
				Cpu_use_rate:      node.Cpu_use_rate,
				Disk:              node.Disk,
				Disk_use_rate:     node.Disk_use_rate,
				Reserve_rate:      node.Reserve_rate,
				Cpu_allocation:    node.Cpu_allocation,    // 这个会在模拟中被修改
				Memory_allocation: node.Memory_allocation, // 这个会在模拟中被修改
				Disk_allocation:   node.Disk_allocation,
			}
		}
	}

	log.Printf("🔄 创建了 %d 个虚拟worker副本，共 %d 个节点", len(virtualWorkers), s.countTotalNodes(virtualWorkers))
	return virtualWorkers
}

// countTotalNodes 计算所有worker的总节点数
func (s *Service) countTotalNodes(workers []models.WorkerInfo) int {
	total := 0
	for _, worker := range workers {
		total += len(worker.Nodes)
	}
	return total
}

// tryAllocateResourceInVirtualWorkers 尝试在虚拟worker中分配资源
// 返回true表示分配成功，false表示资源不足
func (s *Service) tryAllocateResourceInVirtualWorkers(virtualWorkers []models.WorkerInfo, serviceType string, cpuNeed float64, memoryNeed int) bool {
	// 查找匹配服务类型的worker
	for i := range virtualWorkers {
		worker := &virtualWorkers[i]

		// 检查worker类型是否匹配
		if worker.ServerType != serviceType {
			continue
		}

		log.Printf("    🔍 检查worker: %s (类型: %s)", worker.Name, worker.ServerType)

		// 遍历worker的所有节点，寻找可用资源
		for j := range worker.Nodes {
			node := &worker.Nodes[j]

			// 计算节点的可用资源
			// 可用资源 = 总资源 × (1 - 使用率) - 预留资源 - 已分配资源
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			availableCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			availableMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			// 确保可用资源不为负数
			if availableCPU < 0 {
				availableCPU = 0
			}
			if availableMemory < 0 {
				availableMemory = 0
			}

			log.Printf("      📊 节点 %s: 可用CPU=%.2f cores, 可用内存=%d MB",
				node.IP, availableCPU, availableMemory)

			// 检查是否有足够的资源
			if availableCPU >= cpuNeed && availableMemory >= memoryNeed {
				// 资源充足，进行虚拟分配
				node.Cpu_allocation += cpuNeed
				node.Memory_allocation += memoryNeed

				log.Printf("      ✅ 在节点 %s 分配资源成功: CPU=%.2f, Memory=%d",
					node.IP, cpuNeed, memoryNeed)
				log.Printf("      📈 节点 %s 更新后: CPU分配=%.2f, 内存分配=%d",
					node.IP, node.Cpu_allocation, node.Memory_allocation)

				return true
			} else {
				log.Printf("      ❌ 节点 %s 资源不足: 需要CPU=%.2f(可用%.2f), 需要内存=%d(可用%d)",
					node.IP, cpuNeed, availableCPU, memoryNeed, availableMemory)
			}
		}
	}

	log.Printf("    ❌ 没有找到匹配类型 %s 的可用worker或资源不足", serviceType)
	return false
}

// calculateTotalWaitTime 计算总等待时间
// queueLength: 队列总长度
// resourceInsufficientFromIndex: 资源不足开始的位置，-1表示所有记录都有资源
// timeWithResource: 有资源时每个记录的处理时间
// timeWithoutResource: 无资源时每个记录的等待时间
func (s *Service) calculateTotalWaitTime(queueLength int, resourceInsufficientFromIndex int, timeWithResource int, timeWithoutResource int) int {
	if resourceInsufficientFromIndex == -1 {
		// 所有记录都能分配到资源
		totalTime := queueLength * timeWithResource
		log.Printf("📊 时间计算: 所有 %d 个记录都有资源，总时间 = %d × %d = %d 分钟",
			queueLength, queueLength, timeWithResource, totalTime)
		return totalTime
	} else {
		// 部分记录资源不足
		sufficientCount := resourceInsufficientFromIndex
		insufficientCount := queueLength - resourceInsufficientFromIndex

		sufficientTime := sufficientCount * timeWithResource
		insufficientTime := insufficientCount * timeWithoutResource
		totalTime := sufficientTime + insufficientTime

		log.Printf("📊 时间计算: 有资源 %d 个记录(%d分钟), 无资源 %d 个记录(%d分钟), 总时间 = %d 分钟",
			sufficientCount, sufficientTime, insufficientCount, insufficientTime, totalTime)
		return totalTime
	}
}

// getBusinessIDFromServiceID 从 serviceID 获取对应的 businessID
func (s *Service) getBusinessIDFromServiceID(serviceID string) (string, error) {
	// 从 deploy_record 表获取服务信息
	record, err := s.deployRecordRepo.GetByID(serviceID)
	if err != nil {
		return "", fmt.Errorf("获取服务记录失败: %w", err)
	}

	// 解析 customer_envs 获取 SAAS_APP_ID 和 SAAS_TENANT_KEY
	customEnvs, err := parseCustomEnvs(record.CustomerEnvs)
	if err != nil {
		return "", fmt.Errorf("解析环境变量失败: %w", err)
	}

	// 优先使用 BUSINESS_ID 环境变量，如果不存在则使用 SAAS_APP_ID + SAAS_TENANT_KEY 组合
	var businessID string
	if bid, exists := customEnvs["BUSINESS_ID"]; exists && bid != "" {
		businessID = bid
		log.Printf("使用直接指定的 BUSINESS_ID: %s", businessID)
	} else {
		businessID = customEnvs["SAAS_APP_ID"] + customEnvs["SAAS_TENANT_KEY"]
		if businessID == "" {
			return "", fmt.Errorf("无法计算 businessID: BUSINESS_ID 为空且 SAAS_APP_ID 或 SAAS_TENANT_KEY 为空")
		}
		log.Printf("使用组合方式生成 BUSINESS_ID: %s (SAAS_APP_ID=%s + SAAS_TENANT_KEY=%s)",
			businessID, customEnvs["SAAS_APP_ID"], customEnvs["SAAS_TENANT_KEY"])
	}

	return businessID, nil
}

// parseCustomEnvs 解析 customer_envs 数组
func parseCustomEnvs(customerEnvs []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, env := range customerEnvs {
		env = strings.TrimSpace(env)
		if env == "" {
			continue
		}

		// 使用第一个=分隔key和value
		kv := strings.SplitN(env, "=", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			result[key] = value
		}
	}

	return result, nil
}
