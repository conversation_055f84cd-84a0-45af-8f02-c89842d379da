package service

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// DeployRecordWithTrafficInfo 增强版部署记录，专门用于流量统计处理
// 包含原始部署记录信息以及流量统计相关的额外信息
type DeployRecordWithTrafficInfo struct {
	models.DeployRecord
	NetworkTrafficUpdatedAt *time.Time // 上次流量更新时间
	CurrentOSSTraffic       float64    // 当前OSS流量(MB)
}

// BusinessServiceMapping 业务服务映射
// 将多个 service_id 映射到同一个 business_id，避免重复处理
type BusinessServiceMapping struct {
	BusinessID           string                // 业务ID
	ServiceRecords       []models.DeployRecord // 该业务下的所有服务记录
	RepresentativeRecord models.DeployRecord   // 代表性记录，用于OSS配置等
}

// BusinessProcessingGroup 业务流量统计处理分组
// 🚀 完全基于 business_id 的分组，替代原来的 ProcessingGroup
type BusinessProcessingGroup struct {
	StartDate   time.Time // 开始处理的日期
	BusinessIDs []string  // 该组的业务ID列表
}

// BusinessTrafficAccumulator 业务流量累加器
// 🚀 完全基于 business_id 的累加器，替代原来的 ServiceTrafficAccumulator
type BusinessTrafficAccumulator struct {
	totalTraffic       map[string]float64 // businessID -> 累计总流量(MB)
	previousDayTraffic map[string]float64 // businessID -> 前一天流量(MB)
	mu                 sync.RWMutex       // 并发安全锁
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// buildBusinessServiceMappings 构建业务服务映射
// 将多个 service_id 映射到同一个 business_id，避免重复处理
func (s *Service) buildBusinessServiceMappings(records []models.DeployRecord) map[string]*BusinessServiceMapping {
	businessMappings := make(map[string]*BusinessServiceMapping)

	for _, record := range records {
		// 获取 business_id
		businessID, err := s.getBusinessIDFromServiceID(record.ServiceID)
		if err != nil {
			log.Printf("⚠️  获取 businessID 失败 for service %s: %v，跳过该服务", record.ServiceID, err)
			continue
		}

		// 检查是否已有该业务的映射
		if existingMapping, exists := businessMappings[businessID]; exists {
			// 已存在，添加到服务记录列表
			existingMapping.ServiceRecords = append(existingMapping.ServiceRecords, record)
			log.Printf("📝 业务 %s: 添加服务 %s (总计: %d 个服务)",
				businessID, record.ServiceID, len(existingMapping.ServiceRecords))
		} else {
			// 新业务，创建映射
			businessMappings[businessID] = &BusinessServiceMapping{
				BusinessID:           businessID,
				ServiceRecords:       []models.DeployRecord{record},
				RepresentativeRecord: record, // 使用第一个遇到的服务作为代表
			}
			log.Printf("📝 业务 %s: 创建新映射，代表服务: %s", businessID, record.ServiceID)
		}
	}

	log.Printf("🎯 构建业务映射完成: 从 %d 个服务映射到 %d 个业务", len(records), len(businessMappings))
	return businessMappings
}

// StartQueueProcessor 启动队列处理器，使用独立的 goroutine 处理队列和更新状态
func (s *Service) StartQueueProcessor(ctx context.Context) {
	// 使用互斥锁保护状态检查和修改
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	// 检查是否已经在运行
	if s.queueProcessorRunning {
		log.Println("Queue processor is already running, ignoring start request")
		return
	}

	log.Println("Starting queue processor...")

	// 创建可取消的上下文
	processorCtx, cancel := context.WithCancel(ctx)
	s.queueProcessorCancel = cancel
	s.queueProcessorRunning = true

	// 启动一个独立的协程来管理队列处理和状态更新
	go func() {
		// 创建一个等待组，用于等待所有 goroutine 完成
		var wg sync.WaitGroup

		// 启动队列处理 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 队列处理器每 1 分钟运行一次
			queueTicker := time.NewTicker(1 * time.Minute)
			defer queueTicker.Stop()

			// 立即处理一次队列
			s.processQueue(processorCtx)

			// 然后每 3 分钟处理一次
			for {
				select {
				case <-queueTicker.C:
					s.processQueue(processorCtx)
				case <-processorCtx.Done():
					log.Println("Queue processor stopped")
					return
				}
			}
		}()

		// 启动状态更新 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 状态更新器每 2 分钟运行一次
			statusTicker := time.NewTicker(2 * time.Minute)
			defer statusTicker.Stop()

			// 立即更新一次状态
			s.updateServiceStatus(processorCtx)

			// 然后每 2 分钟更新一次
			for {
				select {
				case <-statusTicker.C:
					s.updateServiceStatus(processorCtx)
				case <-processorCtx.Done():
					log.Println("Status updater stopped")
					return
				}
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			// 创建一个停止通道
			stopChan := make(chan struct{})
			// 启动一个独立的 goroutine 来处理停止信号
			go func() {
				<-processorCtx.Done()
				close(stopChan)
			}()
			// 启动资源检查器（会自动立即执行一次）
			s.startResourceUsageChecker(processorCtx, stopChan)

			log.Println("Application resoureuage checker stopped")
		}()

		// 等待上下文取消
		<-processorCtx.Done()
		log.Println("Queue processor context canceled, waiting for goroutines to complete")

		// 等待所有 goroutine 完成
		wg.Wait()

		// 更新状态标志
		s.queueProcessorMu.Lock()
		s.queueProcessorRunning = false
		s.queueProcessorCancel = nil
		s.queueProcessorMu.Unlock()

		log.Println("Queue processor stopped completely")
	}()
}

// StopQueueProcessor 停止队列处理器
func (s *Service) StopQueueProcessor() {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	if !s.queueProcessorRunning {
		log.Println("Queue processor is not running, ignoring stop request")
		return
	}

	log.Println("Stopping queue processor...")

	// 调用取消函数来停止处理器
	if s.queueProcessorCancel != nil {
		s.queueProcessorCancel()
		// 状态更新会在 goroutine 退出时完成
	}
}

// IsQueueProcessorRunning 返回队列处理器是否正在运行
func (s *Service) IsQueueProcessorRunning() bool {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	return s.queueProcessorRunning
}

// processQueue 处理队列中的部署记录
func (s *Service) processQueue(ctx context.Context) {
	// 🔒 添加数据库操作锁，避免与其他定时任务的数据库冲突
	s.dbOperationMutex.Lock()
	defer s.dbOperationMutex.Unlock()

	log.Println("Processing deployment queue...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil || s.workerRepo == nil {
		log.Println("Database connection not available, skipping queue processing")
		return
	}

	// 1. 检查可用的worker数量，设定一次获取部署记录的数量
	availableWorkerCount, err := s.getAvailableWorkerCount(ctx)
	if err != nil {
		log.Printf("Failed to get available worker count: %v", err)
		return
	}

	// 如果可用的worker为0，就不必再获取部署记录了
	if availableWorkerCount == 0 {
		log.Println("No available workers found, skipping queue processing")
		return
	}

	// 根据可用worker数量设定批量大小
	batchSize := s.calculateBatchSize(availableWorkerCount)
	log.Printf("Found %d available workers, setting batch size to %d", availableWorkerCount, batchSize)

	// 2. 批量获取部署记录
	records, err := s.deployRecordRepo.GetNextQueueingRecord(batchSize)
	if err != nil {
		log.Printf("No queueing records found or error: %v", err)
		return
	}

	// 如果获取到的记录为0，结束，等待下一轮的轮询
	if len(records) == 0 {
		log.Println("No queueing records found, waiting for next polling cycle")
		return
	}

	log.Printf("Retrieved %d queueing records for processing", len(records))

	// 3. 逐条部署记录
	processedCount := 0
	skippedCount := 0
	errorCount := 0

	for i, record := range records {
		log.Printf("Processing record %d/%d: %s (%s)", i+1, len(records), record.ServiceID, record.Name)

		// 处理单条部署记录
		result := s.processSingleDeployRecord(ctx, record)

		switch result {
		case "SUCCESS":
			processedCount++
			log.Printf("✅ Record %s processed successfully", record.ServiceID)

			s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelSuccess,
				Title:     fmt.Sprintf("[S:%s] 服务推送至worker", s.GetServiceIP()),
				Content:   fmt.Sprintf("推送服务: %s", record.ServiceID),
				ServiceID: record.ServiceID,
				Metadata:  nil,
				Timestamp: utils.GetCSTTimeString(),
			})

			// 等上一条部署请求返回后，再执行下一个部署记录
			if i < len(records)-1 { // 不是最后一条记录
				log.Printf("Waiting for worker status update before processing next record...")
				time.Sleep(30 * time.Second) // 给Worker充足时间更新状态（异步部署需要更长时间）
			}

		case "NO_WORKER":
			skippedCount++
			log.Printf("⏭️  Record %s skipped (no available worker)", record.ServiceID)
			// 如果没有可用的worker，不必重试，继续下一条

		case "ERROR":
			errorCount++
			log.Printf("❌ Record %s processing failed", record.ServiceID)
			// 错误情况记录日志，不在此处更新状态
			// 状态更新应该在具体的处理逻辑完成时进行
		}
	}

	log.Printf("Queue processing completed: processed=%d, skipped=%d, errors=%d, total=%d",
		processedCount, skippedCount, errorCount, len(records))
}

// getAvailableWorkerCount 获取可用的worker数量
func (s *Service) getAvailableWorkerCount(ctx context.Context) (int, error) {
	// 获取所有AVAILABLE状态的worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{
		Status: "AVAILABLE",
	})
	if err != nil {
		return 0, fmt.Errorf("failed to get available workers: %w", err)
	}

	return len(workers), nil
}

// calculateBatchSize 根据可用worker数量计算批量大小
// 注意：调用此方法前已确保workerCount > 0
func (s *Service) calculateBatchSize(workerCount int) int {
	switch {
	case workerCount <= 3:
		return workerCount + 1 // 小规模集群，批量大小等于worker数 + 1
	case workerCount <= 10:
		return workerCount + 2 // 中等规模，稍微超配 worker数 + 2
	default:
		return min(workerCount, 15) // 大规模集群，限制最大批量为15
	}
}

// processSingleDeployRecord 处理单条部署记录
// 返回值: "SUCCESS", "NO_WORKER", "ERROR"
func (s *Service) processSingleDeployRecord(ctx context.Context, record models.DeployRecord) string {
	workers, err := s.selectAvailableWorker(&record)
	if err != nil {
		log.Printf("Failed to select available worker: %v", err)
		if strings.Contains(err.Error(), "no AVAILABLE workers") {
			return "NO_WORKER"
		}
		// 部署逻辑完成，更新状态为FAILED
		s.updateRecordStatusOnFailure(record.ServiceID, "Worker selection failed: "+err.Error())
		return "ERROR"
	}

	// 调用原有的部署逻辑，但将其封装为可以返回结果的形式
	success := s.executeDeployment(ctx, record, workers)
	if success {
		return "SUCCESS"
	}
	// 部署失败时，不更新状态为FAILED，而是跳过等待下一轮
	log.Printf("No suitable worker available for deployment %s, will retry in next cycle", record.ServiceID)
	return "NO_WORKER"
}

// updateRecordStatusOnFailure 在部署失败时更新记录状态
func (s *Service) updateRecordStatusOnFailure(serviceID, reason string) {
	if updateErr := s.deployRecordRepo.UpdateStatus(serviceID, "FAILED"); updateErr != nil {
		log.Printf("Failed to update record %s status to FAILED: %v", serviceID, updateErr)
	} else {
		log.Printf("Updated record %s status to FAILED due to: %s", serviceID, reason)
	}
}

// executeDeployment 执行部署逻辑（从原有代码提取）
func (s *Service) executeDeployment(ctx context.Context, record models.DeployRecord, workers []models.WorkerInfo) bool {
	// 🆕 检查是否为UPDATE操作
	var selectedCandidate *NodeCandidate

	if record.Remark == "UPDATE" && record.NodeIP != "" && record.WorkerID != "" {
		// UPDATE操作：使用原有的worker_id和node_ip，构造固定的候选节点
		log.Printf("UPDATE operation detected for service %s, using original deployment location: Worker=%s, Node=%s",
			record.ServiceID, record.WorkerID, record.NodeIP)

		selectedCandidate = s.createUpdateCandidate(record, workers)
		if selectedCandidate == nil {
			// 原部署环境不可用，UPDATE失败
			log.Printf("Original deployment environment unavailable for UPDATE operation")
			s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelError,
				Title:     fmt.Sprintf("[S:%s] UPDATE操作失败", s.GetServiceIP()),
				Content:   fmt.Sprintf("服务 %s UPDATE失败: 原部署环境不可用", record.ServiceID),
				ServiceID: record.ServiceID,
				Metadata:  nil,
				Timestamp: utils.GetCSTTimeString(),
			}, 12*time.Hour)
			return false
		}

		// 直接调用统一的部署逻辑
		return s.performActualDeployment(ctx, record, selectedCandidate)

	} else if record.Remark == "UPDATE" {
		// UPDATE操作但没有原部署信息，这是异常情况
		log.Printf("WARNING: UPDATE operation but missing original deployment info (WorkerID=%s, NodeIP=%s), marking as failed",
			record.WorkerID, record.NodeIP)
		s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelError,
			Title:     fmt.Sprintf("[S:%s] UPDATE操作失败", s.GetServiceIP()),
			Content:   fmt.Sprintf("服务 %s UPDATE失败: 缺少原部署信息", record.ServiceID),
			ServiceID: record.ServiceID,
			Metadata:  nil,
			Timestamp: utils.GetCSTTimeString(),
		}, 12*time.Hour)
		return false
	}

	// CREATE操作：执行原本的节点选择逻辑
	log.Printf("CREATE operation for service %s, selecting optimal node", record.ServiceID)

	// 获取部署记录中的资源需求
	// 处理 API 服务的资源需求
	apiReplica := record.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1
		log.Printf("No API replica specified, using default value: 1")
	}
	apiCPU := record.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求
	apiMemory := record.ApiMemory * apiReplica    // API 服务总内存需求

	// 处理 Auto 服务的资源需求
	autoReplica := record.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
		log.Printf("No Auto replica specified, using default value: 0")
	}
	autoCPU := record.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求
	autoMemory := record.AutoMemory * autoReplica    // Auto 服务总内存需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU          // 总 CPU 需求
	totalMemory := apiMemory + autoMemory // 总内存需求

	log.Printf("API service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		apiCPU, record.ApiCpu, apiReplica, apiMemory, record.ApiMemory, apiReplica)
	log.Printf("Auto service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		autoCPU, record.AutoCpu, autoReplica, autoMemory, record.AutoMemory, autoReplica)
	log.Printf("Total deployment requires: CPU=%.2f cores, Memory=%dMB",
		totalCPU, totalMemory)

	// 用于存储所有满足条件的节点
	var candidates []NodeCandidate

	// 遍历所有可用的 worker
	for _, workerInfo := range workers {
		// ✅ 优化：直接使用selectAvailableWorker返回的完整worker信息，无需重新查询
		log.Printf("Checking worker: %s (%s) with %d nodes",
			workerInfo.Name, workerInfo.WorkerID, len(workerInfo.Nodes))

		// 遍历 worker 的所有节点
		for _, node := range workerInfo.Nodes {
			// 计算节点的可用资源
			// 注意：CPU 使用率和内存使用率是百分比值 (0-1)
			// 预留资源：CPU预留0.5核心，内存预留500MB
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			// 设计理念：当前实际可用资源 - 预留资源 - 已分配资源限制（类似k8s limit）
			// 目的：避免多个服务同时达到峰值时资源不足，确保资源隔离
			availableCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			availableMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			log.Printf("Node %s resource details:", node.IP)
			log.Printf("  Total CPU=%.2f cores, Usage=%.2f%%, Used=%.2f cores, Free=%.2f cores",
				node.CPU, node.Cpu_use_rate*100, node.CPU*node.Cpu_use_rate, node.CPU*(1-node.Cpu_use_rate))
			log.Printf("  CPU Reserved=%.2f cores, CPU Allocated=%.2f cores",
				cpuReserved, node.Cpu_allocation)
			log.Printf("  Available CPU = %.2f - %.2f - %.2f = %.2f cores",
				node.CPU*(1-node.Cpu_use_rate), cpuReserved, node.Cpu_allocation, availableCPU)

			log.Printf("  Total Memory=%dMB, Usage=%.2f%%, Used=%dMB, Free=%dMB",
				node.Memory, node.Memory_use_rate*100, int(float64(node.Memory)*node.Memory_use_rate), int(float64(node.Memory)*(1-node.Memory_use_rate)))
			log.Printf("  Memory Reserved=%dMB, Memory Allocated=%dMB",
				memoryReservedMB, node.Memory_allocation)
			log.Printf("  Available Memory = %d - %d - %d = %d MB",
				int(float64(node.Memory)*(1-node.Memory_use_rate)), memoryReservedMB, node.Memory_allocation, availableMemory)

			// 判断节点是否有足够的资源来满足部署需求
			if availableCPU >= totalCPU && availableMemory >= totalMemory {
				// 计算部署后的资源利用率
				// 资源利用率 = 已使用资源 / 总资源
				// 已使用资源 = 当前已使用 + 新部署需要的资源

				// 计算部署后的 CPU 利用率
				newCPUUsage := (node.CPU*node.Cpu_use_rate + totalCPU) / node.CPU

				// 计算部署后的内存利用率
				newMemoryUsage := (float64(node.Memory)*node.Memory_use_rate + float64(totalMemory)) / float64(node.Memory)

				// 计算综合利用率（CPU 和内存的平均值）
				newResourceUsage := (newCPUUsage + newMemoryUsage) / 2

				log.Printf("Node %s after deployment: CPU usage=%.2f%%, Memory usage=%.2f%%, Average usage=%.2f%%",
					node.IP, newCPUUsage*100, newMemoryUsage*100, newResourceUsage*100)

				// 将满足条件的节点添加到候选列表
				candidates = append(candidates, NodeCandidate{
					Worker:          workerInfo,
					NodeIP:          node.IP,
					ResourceUsage:   newResourceUsage,
					CPUUsage:        newCPUUsage,
					MemoryUsage:     newMemoryUsage,
					CPUAllocated:    totalCPU*node.Reserve_rate + node.Cpu_allocation,
					MemoryAllocated: int(float64(totalMemory)*node.Reserve_rate) + node.Memory_allocation,
					DiskAllocated:   0, // 暂时不考虑磁盘
				})

				log.Printf("Added node %s to candidates list with resource usage %.2f%%",
					node.IP, newResourceUsage*100)
			} else {
				log.Printf("Node %s does not have enough resources. Required: CPU=%.2f, Memory=%d; Available: CPU=%.2f, Memory=%d",
					node.IP, totalCPU, totalMemory, availableCPU, availableMemory)
			}
		}
	}

	// 如果没有找到合适的节点，返回失败
	if len(candidates) == 0 {
		log.Printf("No suitable node found for deployment %s", record.ServiceID)

		s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelWarning,
			Title:     fmt.Sprintf("[S:%s] 没有找到合适的节点", s.GetServiceIP()),
			Content:   "没有找到合适的节点, 操作： " + record.Remark,
			ServiceID: record.ServiceID,
			Metadata:  nil,
			Timestamp: utils.GetCSTTimeString(),
		}, 12*time.Hour)
		return false
	}

	// 按照资源利用率排序（从低到高）
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].ResourceUsage < candidates[j].ResourceUsage
	})

	// 输出所有候选节点的信息
	log.Printf("Found %d suitable nodes for deployment:", len(candidates))
	for i, candidate := range candidates {
		log.Printf("  %d. Worker=%s, Node=%s, Resource usage=%.2f%%, CPU usage=%.2f%%, Memory usage=%.2f%%",
			i+1, candidate.Worker.WorkerID, candidate.NodeIP,
			candidate.ResourceUsage*100, candidate.CPUUsage*100, candidate.MemoryUsage*100)
	}

	// 遍历候选节点，检查 Worker 的健康状态

	for i, candidate := range candidates {
		// 创建 Worker 客户端，调用健康检查接口
		workerClient := NewWorkerHTTPClient(candidate.Worker.Host)

		// 调用 Worker 的健康检查接口
		log.Printf("Checking real-time health status of worker %s (%s)",
			candidate.Worker.WorkerID, candidate.Worker.Host)

		// 设置健康检查超时，稍微延长以确保获取准确状态
		healthCtx, cancel := context.WithTimeout(ctx, 8*time.Second)
		healthResp, err := workerClient.HealthCheck(healthCtx)
		cancel() // 立即释放资源

		if err != nil {
			log.Printf("Failed to check health status of worker %s: %v",
				candidate.Worker.WorkerID, err)
			// 健康检查失败，可能worker不可用，跳过此候选节点
			continue
		}

		// 检查响应码
		if healthResp.Code != 200 {
			log.Printf("Worker %s health check returned error code %d: %s",
				candidate.Worker.WorkerID, healthResp.Code, healthResp.Msg)
			continue
		}

		// 检查 Worker 的状态
		statusData, ok := healthResp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid health response format from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果响应格式不正确，尝试下一个候选节点
		}

		status, ok := statusData["status"].(string)
		if !ok {
			log.Printf("Invalid status format in health response from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果状态格式不正确，尝试下一个候选节点
		}

		log.Printf("Worker %s real-time health status: %s", candidate.Worker.WorkerID, status)

		// 只有 Worker 状态为 AVAILABLE 时才选择该节点
		if status == "AVAILABLE" {
			selectedCandidate = &candidates[i]
			log.Printf("✅ Selected worker %s with confirmed AVAILABLE status",
				candidate.Worker.WorkerID)
			break
		} else {
			log.Printf("❌ Worker %s is in %s status, checking next candidate",
				candidate.Worker.WorkerID, status)
		}
	}

	// 如果没有找到可用的 Worker，跳过当前记录等待下一轮
	if selectedCandidate == nil {
		log.Printf("No worker with AVAILABLE status found for deployment %s, skipping for now",
			record.ServiceID)
		return false
	}

	// 执行实际的部署操作
	return s.performActualDeployment(ctx, record, selectedCandidate)
}

// performActualDeployment 执行实际的部署操作
func (s *Service) performActualDeployment(ctx context.Context, record models.DeployRecord, selectedCandidate *NodeCandidate) bool {
	log.Printf("Final node selection: Worker=%s, Node=%s, Resource usage after deployment=%.2f%%",
		selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP, selectedCandidate.ResourceUsage*100)

	// 获取选中的 worker 和节点
	selectedWorker := selectedCandidate.Worker
	selectedNodeIP := selectedCandidate.NodeIP
	selectedLabels := selectedCandidate.Worker.Labels

	// 更新部署记录的 worker ID 和节点 IP
	record.WorkerID = selectedWorker.WorkerID
	record.NodeIP = selectedNodeIP
	record.Status = "PROCESSING" // 更新状态为处理中
	record.HostIP = selectedWorker.HostIP

	// 获取 worker 的 domain_suffix
	if record.DomainSuffix == "" && selectedWorker.DomainSuffix != "" {
		record.DomainSuffix = selectedWorker.DomainSuffix
		log.Printf("Using domain suffix from worker: %s", record.DomainSuffix)
	}

	// 通过 record.ImageName 获取数据库中对应的 imageURL 和 ports 数据
	image, err := s.imageTypeRepo.GetByName(record.ImageName)
	if err != nil {
		log.Printf("Failed to get image information for image name %s: %v", record.ImageName, err)

		// 区分错误类型
		errorMsg := err.Error()
		if strings.Contains(errorMsg, "no rows in result set") ||
			strings.Contains(errorMsg, "not found") ||
			strings.Contains(errorMsg, "no such image") {

			// 镜像不存在 - 永久性错误
			log.Printf("Image '%s' does not exist in database, marking as ERROR", record.ImageName)

			record.Status = "ERROR"
			record.Remark = "镜像不存在"

			if updateErr := s.deployRecordRepo.Update(record); updateErr != nil {
				log.Printf("Failed to update record %s status to ERROR: %v", record.ServiceID, updateErr)
			} else {
				log.Printf("✅ Updated record %s status to ERROR due to image not found: %s",
					record.ServiceID, record.ImageName)
			}

			s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelError,
				Title:     fmt.Sprintf("[S:%s] 镜像不存在", s.GetServiceIP()),
				Content:   "镜像不存在",
				ServiceID: record.ServiceID,
				Metadata:  nil,
				Timestamp: utils.GetCSTTimeString(),
			}, 120*time.Minute)

		} else {
			// 其他错误 - 可能是临时性错误
			log.Printf("Temporary error getting image info for %s, will retry later: %v",
				record.ImageName, err)
		}

		return false
	}

	// 记录获取到的镜像信息
	log.Printf("Found image for %s: URL=%s, Ports=%v", record.ImageName, image.ImageURL, image.Ports)

	// 创建 worker 客户端
	workerClient := NewWorkerHTTPClient(selectedWorker.Host)

	// 创建基础标签
	baseLabels := []string{
		"system=zero-ops-platform_app-deploy",
		"service-type=" + record.ServiceType,
	}

	// 将 image.Labels 聚合到标签中，并进行排重处理
	allLabels := utils.MergeLabelsWithDeduplication(baseLabels, selectedLabels, record.Labels, image.Labels)
	record.Labels = allLabels

	// 创建部署请求
	workerReq := &models.WorkerDeployRequest{
		ImageName:       record.ImageName, // 使用镜像类型作为镜像名称
		ImageURL:        image.ImageURL,
		ServiceType:     record.ServiceType,  // 🆕 传递服务类型，确保Worker层能正确识别
		DomainPrefix:    record.DomainPrefix, // 从 deploy_record 表获取的域名前缀
		DomainSuffix:    record.DomainSuffix, // 从 workers 表获取的域名后缀
		NodeIP:          selectedNodeIP,      // 使用选中的节点 IP
		HostIP:          record.HostIP,
		Expiration:      record.Expiration,      // 设置过期时间
		DurationSeconds: record.DurationSeconds, // 设置持续时间（秒）
		ApiCpu:          record.ApiCpu,          // 使用 API CPU 作为 CPU 核心数
		ApiMemory:       record.ApiMemory,       // 使用 API 内存作为内存大小
		ApiReplica:      record.ApiReplica,
		AutoCpu:         record.AutoCpu,
		AutoMemory:      record.AutoMemory,
		AutoReplica:     record.AutoReplica,
		ServiceId:       record.ServiceID,    // 使用服务 ID 作为容器名称
		CustomerEnvs:    record.CustomerEnvs, // 用户自定义环境变量
		Ports:           image.Ports,         // 从 image_type 表获取的容器端口列表
		Labels:          allLabels,           // 添加聚合后的标签
	}

	// 记录所有标签
	log.Printf("Using %d labels for deployment: %v", len(allLabels), allLabels)

	// 记录将要发送的域名和端口信息
	log.Printf("Sending domain information to worker: prefix=%s, suffix=%s",
		record.DomainPrefix, record.DomainSuffix)
	log.Printf("Sending ports information to worker: %v", image.Ports)

	// 部署前再次确认Worker状态（双重保险）
	log.Printf("Final confirmation: checking worker %s status before deployment", selectedWorker.WorkerID)
	finalHealthCtx, finalCancel := context.WithTimeout(ctx, 5*time.Second)
	finalHealthResp, err := workerClient.HealthCheck(finalHealthCtx)
	finalCancel()

	if err != nil {
		log.Printf("Final health check failed for worker %s: %v", selectedWorker.WorkerID, err)
		return false
	}

	if finalHealthResp.Code != 200 {
		log.Printf("Final health check returned error for worker %s: %s", selectedWorker.WorkerID, finalHealthResp.Msg)
		return false
	}

	if finalStatusData, ok := finalHealthResp.Data.(map[string]interface{}); ok {
		if finalStatus, ok := finalStatusData["status"].(string); ok && finalStatus != "AVAILABLE" {
			log.Printf("Worker %s status changed to %s before deployment, aborting", selectedWorker.WorkerID, finalStatus)
			return false
		}
	}

	// 根据 remark 字段调用不同的 Worker 方法
	log.Printf("Sending deployment request to worker %s, node %s, remark: %s", selectedWorker.WorkerID, selectedNodeIP, record.Remark)

	// 设置部署请求超时（部署可能需要较长时间）
	deployCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	// 🆕 根据 remark 字段判断操作类型
	var workerResp *models.WorkerResponse
	switch record.Remark {
	case "UPDATE":
		log.Printf("Calling Worker UpdateContainer for service %s", record.ServiceID)
		workerResp, err = workerClient.UpdateContainer(deployCtx, workerReq)

	case "DEPLOY", "":
		log.Printf("Calling Worker DeployContainer for service %s", record.ServiceID)
		workerResp, err = workerClient.DeployContainer(deployCtx, workerReq)

	default:
		log.Printf("Unknown remark '%s' for service %s, treating as DEPLOY", record.Remark, record.ServiceID)
		workerResp, err = workerClient.DeployContainer(deployCtx, workerReq)
	}

	if err != nil {
		log.Printf("Failed to execute %s operation for service %s: %v", record.Remark, record.ServiceID, err)
		return false
	}

	// 检查响应
	if workerResp.Code != 200 {
		log.Printf("Worker returned error: %s", workerResp.Msg)
		// 检查是否是因为Worker状态问题导致的失败
		if strings.Contains(strings.ToLower(workerResp.Msg), "freeze") ||
			strings.Contains(strings.ToLower(workerResp.Msg), "busy") ||
			strings.Contains(strings.ToLower(workerResp.Msg), "unavailable") {
			log.Printf("Deployment failed due to worker status issue: %s", workerResp.Msg)
		}
		return false
	}

	// 更新service record（包含状态和其他信息）
	if err = s.deployRecordRepo.Update(record); err != nil {
		log.Printf("Failed to update deploy record with selected worker and node: %v", err)
		return false
	}

	// 更新 worker 资源使用情况到数据库
	log.Printf("Updating resource allocation for worker %s, node %s",
		selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP)

	// 🔑 数据库操作已在 processQueue 方法中通过 dbOperationMutex 保护
	log.Printf("🔒 Database operations protected by dbOperationMutex, proceeding with deployment resource allocation update for %s", selectedCandidate.Worker.WorkerID)

	// 🔑 重要：重新获取最新的worker信息，确保数据一致性
	// 避免使用可能过期的selectedCandidate.Worker数据
	currentWorker, err := s.workerRepo.GetByID(selectedCandidate.Worker.WorkerID)
	if err != nil {
		log.Printf("Failed to get fresh worker info for deployment resource allocation: %v", err)
		return false
	}
	log.Printf("🔄 Fetched fresh worker info from database for %s under lock protection", selectedCandidate.Worker.WorkerID)

	// 查找对应的节点并更新资源分配
	nodeUpdated := false
	for i := range currentWorker.Nodes {
		if currentWorker.Nodes[i].IP == selectedCandidate.NodeIP {
			// 重新计算资源分配值（基于最新的节点数据）
			// 获取服务的资源需求
			totalCPU := record.ApiCpu * float64(record.ApiReplica)
			totalMemory := float64(record.ApiMemory) * float64(record.ApiReplica)

			// 计算新的分配值（累加模式）
			newCPUAllocated := totalCPU*currentWorker.Nodes[i].Reserve_rate + currentWorker.Nodes[i].Cpu_allocation
			newMemoryAllocated := int(totalMemory*currentWorker.Nodes[i].Reserve_rate) + currentWorker.Nodes[i].Memory_allocation

			// 更新节点的资源分配数据
			log.Printf("Updating node %s resource allocation: CPU=%.2f→%.2f, Memory=%d→%d, Disk=%d→%d",
				selectedCandidate.NodeIP,
				currentWorker.Nodes[i].Cpu_allocation, newCPUAllocated,
				currentWorker.Nodes[i].Memory_allocation, newMemoryAllocated,
				currentWorker.Nodes[i].Disk_allocation, 0)

			currentWorker.Nodes[i].Cpu_allocation = newCPUAllocated
			currentWorker.Nodes[i].Memory_allocation = newMemoryAllocated
			// Disk_allocation 暂时不更新，保持原值
			nodeUpdated = true
			break
		}
	}

	if nodeUpdated {
		// 更新数据库中的 worker 信息
		fmt.Println(currentWorker, "================")
		if err := s.workerRepo.Update(currentWorker); err != nil {
			fmt.Printf("Failed to update worker resource allocation in database: %v", err)
		} else {
			fmt.Printf("Successfully updated resource allocation for worker %s, node %s",
				selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP)
		}
	} else {
		fmt.Printf("Warning: Node %s not found in worker %s for resource allocation update",
			selectedCandidate.NodeIP, selectedCandidate.Worker.WorkerID)
	}

	log.Printf("Successfully deployed service %s", record.ServiceID)
	return true
}

// NodeCandidate 定义节点候选者结构体（移到包级别以便复用）
type NodeCandidate struct {
	Worker          models.WorkerInfo // Worker 信息
	NodeIP          string            // 节点 IP
	ResourceUsage   float64           // 部署后的资源利用率
	CPUUsage        float64           // 部署后的 CPU 利用率
	MemoryUsage     float64           // 部署后的内存利用率
	CPUAllocated    float64           // 部署后节点已分配的 CPU 核心数
	MemoryAllocated int               // 部署后节点已分配的内存大小（MB）
	DiskAllocated   int               // 部署后节点已使用的磁盘空间（MB）
}

// selectAvailableWorker 选择可用的 worker 列表
func (s *Service) selectAvailableWorker(record *models.DeployRecord) ([]models.WorkerInfo, error) {
	if s.workerRepo == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// 如果 record.WorkerID 不为空，检查 workers 表里是否有这个 worker
	if record.WorkerID != "" {
		log.Printf("Record has WorkerID: %s, checking if worker exists", record.WorkerID)

		// 尝试从数据库获取指定的 worker
		worker, err := s.workerRepo.GetByID(record.WorkerID)
		if err == nil && worker.WorkerID == record.WorkerID {
			// worker 存在直接返回这个 worker
			log.Printf("Found existing worker %s for record %s", worker.WorkerID, record.ServiceID)
			return []models.WorkerInfo{worker}, nil
		}

		// worker 不存在 - 永久性错误，更新记录状态
		log.Printf("Worker %s not found, marking record as ERROR", record.WorkerID)

		// 更新记录状态为 ERROR，remark 为具体错误原因
		record.Status = "ERROR"
		record.Remark = fmt.Sprintf("指定Worker不存在: %s", record.WorkerID)

		if updateErr := s.deployRecordRepo.Update(*record); updateErr != nil {
			log.Printf("Failed to update record %s status to ERROR: %v", record.ServiceID, updateErr)
		} else {
			log.Printf("✅ Updated record %s status to ERROR due to worker not found: %s",
				record.ServiceID, record.WorkerID)
		}

		return nil, fmt.Errorf("worker %s not found or not AVAILABLE", record.WorkerID)
	}

	// 获取所有与部署记录服务类型匹配的 worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{
		ServerType: record.ServiceType,
		Status:     "AVAILABLE", // 只选择活跃状态的 worker
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get workers from database: %w", err)
	}

	// 检查是否有可用的 worker
	if len(workers) == 0 {
		return nil, fmt.Errorf("no AVAILABLE workers available for service type: %s", record.ServiceType)
	}

	log.Printf("Found %d AVAILABLE workers for service type: %s", len(workers), record.ServiceType)
	return workers, nil
}

// updateServiceStatus 更新服务状态
//  1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
//  2. 按数据中 worker_id 字段进行分组
//     a. 通过 worker_id 从 workers 表获取 host
//     b. 把同组的 service_id 用逗号拼接到一起
//  3. 调用 worker 层接口 worker.GET("/status", workerCtrl.GetDeployStatus) 获取相关数据
func (s *Service) updateServiceStatus(ctx context.Context) error {
	// 🔒 添加数据库操作锁，避免与其他定时任务的数据库冲突
	s.dbOperationMutex.Lock()
	defer s.dbOperationMutex.Unlock()

	log.Println("Updating service status...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil || s.workerRepo == nil {
		log.Println("Database connection not available, skipping service status update")
		return fmt.Errorf("database connection not available")
	}

	// 1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
	records, err := s.deployRecordRepo.GetRecordsByStatus([]string{"PROCESSING", "RUNNING", "FAILED"})
	if err != nil {
		log.Printf("Failed to get deploy records: %v", err)
		return fmt.Errorf("failed to get deploy records: %w", err)
	}

	if len(records) == 0 {
		log.Println("No PROCESSING or RUNNING records found, skipping service status update")
		return nil
	}

	log.Printf("Found %d PROCESSING or RUNNING or FAILED records", len(records))

	// 2. 按数据中 worker_id 字段进行分组
	workerGroups := make(map[string][]models.DeployRecord)
	for _, record := range records {
		if record.WorkerID != "" {
			workerGroups[record.WorkerID] = append(workerGroups[record.WorkerID], record)
		} else if record.Status == "PROCESSING" && record.Remark == "DELETE_REQUESTED" {
			// 处理删除请求但没有WorkerID的异常情况
			log.Printf("Found orphaned DELETE_REQUESTED record without WorkerID: %s, updating to STOPPED", record.ServiceID)

			if err := s.deployRecordRepo.UpdateStatus(record.ServiceID, "STOPPED"); err != nil {
				log.Printf("Failed to update orphaned DELETE_REQUESTED record status: %v", err)
			} else {
				log.Printf("Successfully updated orphaned DELETE_REQUESTED record %s to STOPPED", record.ServiceID)

				// 发送通知
				s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
					Level:     utils.NotificationLevelInfo,
					Title:     fmt.Sprintf("[S:%s] 无worker情况，删除请求记录", s.GetServiceIP()),
					Content:   fmt.Sprintf("无worker情况，删除请求记录: %s", record.ServiceID),
					ServiceID: record.ServiceID,
					Metadata:  nil,
					Timestamp: utils.GetCSTTimeString(),
				})
			}
		}
	}

	if len(workerGroups) == 0 {
		log.Println("No records with valid worker_id found, skipping service status update")
		return nil
	}

	// 遍历每个 worker 组
	for workerID, workerRecords := range workerGroups {
		// a. 通过 worker_id 从 workers 表获取 host
		workerInfo, err := s.workerRepo.GetByID(workerID)
		if err != nil {
			log.Printf("Failed to get worker info for worker_id %s: %v", workerID, err)
			continue
		}

		if workerInfo.Host == "" {
			log.Printf("Worker %s has no host information, skipping", workerID)
			continue
		}

		// b. 把同组的 service_id 用逗号拼接到一起
		var serviceIDs []string
		for _, record := range workerRecords {
			serviceIDs = append(serviceIDs, record.ServiceID)
		}

		log.Printf("Querying status for %d services from worker %s (%s)", len(serviceIDs), workerID, workerInfo.Host)

		// 3. 调用 worker 层接口获取相关数据
		workerClient := NewWorkerHTTPClient(workerInfo.Host)
		resp, err := workerClient.GetDeployStatus(ctx, serviceIDs)
		if err != nil {
			log.Printf("Failed to get deploy status from worker %s: %v", workerID, err)
			continue
		}

		// 处理响应
		if resp.Code != 200 {
			log.Printf("Worker %s returned error: %s", workerID, resp.Msg)
			continue
		}

		// 解析响应数据
		data, ok := resp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid response format from worker %s", workerID)
			continue
		}

		// 统一处理记录，将单条记录转换为数组格式
		var recordsToProcess []map[string]interface{}

		// 检查是否是单条记录格式（直接包含 service_id 和 status）
		if serviceID, hasServiceID := data["service_id"].(string); hasServiceID {
			if status, hasStatus := data["status"].(string); hasStatus {
				// 将单条记录转换为数组格式
				recordsToProcess = []map[string]interface{}{
					{
						"service_id": serviceID,
						"status":     status,
					},
				}
			}
		} else if records, hasRecords := data["records"].([]interface{}); hasRecords {
			// 多条记录的情况，转换为统一格式
			for _, record := range records {
				if recordMap, isMap := record.(map[string]interface{}); isMap {
					recordsToProcess = append(recordsToProcess, recordMap)
				}
			}
		} else {
			log.Printf("Response from worker %s does not contain valid records", workerID)
			continue
		}

		// 处理所有记录
		for _, recordMap := range recordsToProcess {
			serviceID, ok := recordMap["service_id"].(string)
			if !ok {
				log.Printf("Invalid service_id format in record from worker %s", workerID)
				continue
			}

			status, ok := recordMap["status"].(string)
			if !ok {
				log.Printf("Invalid status format in record from worker %s", workerID)
				continue
			}

			// 提取 visited_at 字段（如果存在）
			var visitedAt string
			if visitedAtValue, hasVisitedAt := recordMap["visited_at"]; hasVisitedAt {
				if visitedAtStr, isString := visitedAtValue.(string); isString && visitedAtStr != "" {
					visitedAt = visitedAtStr
				}
			}

			// 这里添加判断，如果 status 的值为以下几种情况之一，才进行更新，否则跳过
			// RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			validStatuses := map[string]bool{
				"RUNNING":     true,
				"TERMINATION": true,
				"STOPPED":     true,
				"FAILED":      true,
			}

			if !validStatuses[status] {
				log.Printf("Skipping update for service %s with invalid status: %s", serviceID, status)
				continue
			}

			// 更新数据库中的状态，同时同步 visited_at 字段
			if visitedAt != "" {
				// 如果有 visited_at 数据，获取完整记录并更新
				record, err := s.deployRecordRepo.GetByID(serviceID)
				if err != nil {
					log.Printf("Failed to get record for service %s: %v", serviceID, err)
					// 回退到只更新状态
					if err := s.deployRecordRepo.UpdateStatus(serviceID, status); err != nil {
						log.Printf("Failed to update status for service %s: %v", serviceID, err)
					}
				} else {
					// 更新状态和 visited_at
					record.Status = status
					record.VisitedAt = visitedAt
					if err := s.deployRecordRepo.Update(record); err != nil {
						log.Printf("Failed to update record for service %s: %v", serviceID, err)
					} else {
						log.Printf("Updated status for service %s to %s and synced visited_at: %s", serviceID, status, visitedAt)
					}
				}
			} else {
				// 没有 visited_at 数据，只更新状态
				if err := s.deployRecordRepo.UpdateStatus(serviceID, status); err != nil {
					log.Printf("Failed to update status for service %s: %v", serviceID, err)
				} else {
					log.Printf("Updated status for service %s to %s", serviceID, status)
				}
			}
			log.Printf("Service %s status changed to %s, resource allocation will be updated by periodic task", serviceID, status)

			// 如果status=="FAILED", 需要发送通知
			if status == "FAILED" {
				// 获取服务详细信息用于通知
				record, err := s.deployRecordRepo.GetByID(serviceID)
				if err != nil {
					log.Printf("Failed to get service record for notification: %v", err)
				} else {
					s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
						Level:     utils.NotificationLevelError,
						Title:     fmt.Sprintf("[S:%s] 服务部署失败", s.GetServiceIP()),
						Content:   fmt.Sprintf("服务部署失败, 服务 service_id: %s, 集群 worker_id: %s", serviceID, record.WorkerID),
						ServiceID: serviceID,
						Metadata:  nil,
						Timestamp: utils.GetCSTTimeString(),
					}, 120*time.Minute)
				}
			}
		}

		// 处理未找到的记录
		notFound, ok := data["not_found"].([]interface{})
		if ok && len(notFound) > 0 {
			for _, nfID := range notFound {
				serviceID, ok := nfID.(string)
				if !ok {
					continue
				}
				log.Printf("Service %s not found on worker %s", serviceID, workerID)

				// 将未找到的记录标记为 UNKNOWN 状态
				if err := s.deployRecordRepo.UpdateStatus(serviceID, "UNKNOWN"); err != nil {
					log.Printf("Failed to update status for not found service %s: %v", serviceID, err)
				}
				s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
					Level:     utils.NotificationLevelSuccess,
					Title:     fmt.Sprintf("[S:%s] 数据不一致，出现游离record", s.GetServiceIP()),
					Content:   "数据不一致，出现游离record",
					ServiceID: serviceID,
					Metadata:  nil,
					Timestamp: utils.GetCSTTimeString(),
				})
			}
		}
	}

	log.Println("Service status update completed")
	return nil
}

// ==================== 独立的资源检查逻辑 ====================

// 注意：checkResourceUsageUnified 方法已废弃
// 功能已分离为 checkHardwareResourceUsage 和 checkOSSResourceUsage 两个独立方法

// getRunningServicesForOSS 获取用于OSS统计的服务列表（优化版）
// 包含：1. 当前运行中的服务 (RUNNING, PROCESSING)  2. 最近停止的服务 (STOPPED/FAILED/TERMINATED/UNKNOWN 且 updated_at > 昨天00:00:00)
// 解决服务起停频繁导致的OSS统计数据偏差问题，同时避免统计范围过大
func (s *Service) getRunningServicesForOSS(ctx context.Context) ([]models.DeployRecord, error) {
	if s.deployRecordRepo == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	// 计算昨天00:00:00 (CST时区)
	now := time.Now().In(time.FixedZone("CST", 8*3600))
	yesterday := now.AddDate(0, 0, -1)
	yesterdayStart := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())

	log.Printf("OSS统计查询条件: 当前时间=%s, 昨天开始时间=%s",
		now.Format("2006-01-02 15:04:05"),
		yesterdayStart.Format("2006-01-02 15:04:05"))

	// 获取OSS统计记录：当前运行中的服务 + 最近有状态变更的服务
	records, err := s.deployRecordRepo.GetRecordsForOSSStatistics(yesterdayStart)
	if err != nil {
		return nil, fmt.Errorf("获取OSS统计记录失败: %w", err)
	}

	log.Printf("OSS统计查询结果: 共获取到 %d 条记录", len(records))

	// 统计不同状态的记录数量（用于日志分析）
	statusCount := make(map[string]int)
	for _, record := range records {
		statusCount[record.Status]++
	}
	log.Printf("OSS统计记录状态分布: %+v", statusCount)

	return records, nil
}

// getRunningServices 获取用于硬件资源统计的服务列表
// 仅包含当前真正运行中的服务 (RUNNING, PROCESSING)，用于准确计算硬件资源分配
func (s *Service) getRunningServices(ctx context.Context) ([]models.DeployRecord, error) {
	if s.deployRecordRepo == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	log.Printf("硬件资源统计查询: 仅获取RUNNING和PROCESSING状态的服务")

	// 获取硬件资源统计记录：仅当前运行中的服务
	records, err := s.deployRecordRepo.GetRecordsByStatus([]string{"RUNNING", "PROCESSING"})
	if err != nil {
		return nil, fmt.Errorf("获取硬件资源统计记录失败: %w", err)
	}

	log.Printf("硬件资源统计查询结果: 共获取到 %d 条记录", len(records))

	// 统计不同状态的记录数量（用于日志分析）
	statusCount := make(map[string]int)
	for _, record := range records {
		statusCount[record.Status]++
	}
	log.Printf("硬件资源统计记录状态分布: %+v", statusCount)

	return records, nil
}

func (s *Service) startResourceUsageChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting resource usage checkers...")

	var wg sync.WaitGroup

	// 启动硬件资源检查器（始终启用，15分钟间隔）
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.startHardwareResourceChecker(ctx, stopChan)
	}()

	// 启动OSS资源检查器（可选启用，12小时间隔）
	if utils.GetEnvBool("ENABLE_OSS_MONITORING", false) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.startOSSResourceChecker(ctx, stopChan)
		}()
	} else {
		log.Println("OSS monitoring disabled, skipping OSS resource checker")
	}

	// 等待停止信号
	<-stopChan
	log.Println("Received stop signal, stopping all resource checkers")

	// 等待所有检查器停止
	wg.Wait()
	log.Println("All resource checkers stopped")
}

// startHardwareResourceChecker 启动硬件资源检查器
func (s *Service) startHardwareResourceChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting hardware resource checker with 15 minutes interval")

	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	// 立即执行一次
	log.Println("Performing initial hardware resource check")
	s.checkHardwareResourceUsage(ctx)

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			log.Println("Hardware resource checker triggered")
			s.checkHardwareResourceUsage(ctx)
		case <-stopChan:
			log.Println("Hardware resource checker stopped")
			return
		}
	}
}

// startOSSResourceChecker 启动OSS资源检查器
func (s *Service) startOSSResourceChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting OSS resource checker with 2 hours interval")

	ticker := time.NewTicker(12 * time.Hour)
	defer ticker.Stop()

	// 立即执行一次
	log.Println("Performing initial OSS resource check")
	s.checkOSSResourceUsage(ctx)

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			log.Println("OSS resource checker triggered")
			s.checkOSSResourceUsage(ctx)
		case <-stopChan:
			log.Println("OSS resource checker stopped")
			return
		}
	}
}

// checkHardwareResourceUsage 检查服务器硬件资源使用情况
// 专注于CPU、内存、磁盘等硬件资源的分配统计
func (s *Service) checkHardwareResourceUsage(ctx context.Context) {
	// 🔒 添加数据库操作锁，避免与其他定时任务的数据库冲突
	s.dbOperationMutex.Lock()
	defer s.dbOperationMutex.Unlock()

	log.Println("开始检查服务器硬件资源使用情况...")

	// 获取运行中的服务
	runningRecords, err := s.getRunningServices(ctx)
	if err != nil {
		log.Printf("获取运行中服务失败: %v", err)
		return
	}

	if len(runningRecords) == 0 {
		log.Println("没有运行中或处理中的服务，需要清零所有节点的资源分配")
	} else {
		log.Printf("找到 %d 个运行中或处理中的服务，开始更新硬件资源分配", len(runningRecords))
	}

	// 🔑 关键修复：无论是否有运行中的服务，都要更新资源分配状态
	// 如果 runningRecords 为空，会清零所有节点的分配
	s.updateHardwareResourceAllocation(runningRecords)

	log.Printf("硬件资源检查完成，处理了 %d 个服务", len(runningRecords))
}

// checkOSSResourceUsage OSS资源使用检查
// 包含：OSS存储统计、OSS流量统计、日志预拉取
func (s *Service) checkOSSResourceUsage(ctx context.Context) {
	// 🔒 添加数据库操作锁，避免与其他定时任务的数据库冲突
	s.dbOperationMutex.Lock()
	defer s.dbOperationMutex.Unlock()

	log.Println("开始OSS资源使用检查（12小时周期）...")

	// 获取运行中的服务
	runningRecords, err := s.getRunningServicesForOSS(ctx)
	if err != nil {
		log.Printf("获取运行中服务失败: %v", err)
		return
	}

	if len(runningRecords) == 0 {
		log.Println("没有运行中的服务，跳过OSS资源检查")
		return
	}

	// 计算当前时间 - 使用东八区时间
	currentTime := utils.GetCSTTime()

	// 🔄 改为串行执行OSS相关任务，避免内部数据库冲突

	// 任务1: OSS存储量和文件数统计
	log.Println("开始执行OSS存储量和文件数统计...")
	s.processOSSStorageStats(ctx, runningRecords)

	// 任务2: OSS流量统计
	log.Println("开始执行OSS流量统计...")
	s.processOSSTrafficStats(ctx, runningRecords, currentTime)

	log.Printf("OSS资源检查完成，处理了 %d 个服务", len(runningRecords))
}

// processOSSStorageStats 处理OSS存储量和文件数统计（基于 business_id 优化）
// 🆕 使用新的临时OSS管理器架构
func (s *Service) processOSSStorageStats(ctx context.Context, records []models.DeployRecord) {
	log.Printf("🚀 开始处理 %d 个服务的OSS存储统计", len(records))

	if len(records) == 0 {
		log.Printf("📭 没有需要处理的Records")
		return
	}

	// 提取唯一的 business_id，避免重复处理
	businessIDToRecord := make(map[string]models.DeployRecord)
	for _, record := range records {
		businessID, err := s.getBusinessIDFromServiceID(record.ServiceID)
		if err != nil {
			log.Printf("获取 businessID 失败 for service %s: %v", record.ServiceID, err)
			continue
		}

		// 如果同一个业务有多个服务，使用第一个遇到的服务记录作为代表
		if _, exists := businessIDToRecord[businessID]; !exists {
			businessIDToRecord[businessID] = record
		}
	}

	log.Printf("📊 从 %d 个服务中提取到 %d 个唯一业务进行存储统计", len(records), len(businessIDToRecord))

	// 🆕 按Worker分组Records
	workerGroups := s.groupRecordsByWorkerForBusiness(businessIDToRecord)
	log.Printf("📊 分组结果: %d个Worker", len(workerGroups))

	// 🆕 串行处理每个Worker组
	successCount := 0
	failureCount := 0

	for workerID, businessRecords := range workerGroups {
		select {
		case <-ctx.Done():
			log.Printf("⏹️ 上下文取消，停止处理Worker %s", workerID)
			return
		default:
		}

		// 为该Worker创建临时OSS管理器
		if len(businessRecords) == 0 {
			continue
		}

		// 使用第一个Record作为样本创建管理器
		sampleRecord := businessRecords[0].Record
		ossManager, err := CreateOSSManagerForWorker(workerID, sampleRecord)
		if err != nil {
			log.Printf("❌ Worker %s OSS管理器创建失败: %v", workerID, err)
			failureCount += len(businessRecords)
			continue
		}

		log.Printf("✅ Worker %s OSS管理器创建成功，处理 %d 个业务", workerID, len(businessRecords))

		// 批量处理该Worker下的所有业务
		for _, businessRecord := range businessRecords {
			if err := s.processBusinessOSSStorage(ctx, businessRecord, ossManager); err != nil {
				failureCount++
				log.Printf("❌ 业务 %s 处理失败: %v", businessRecord.BusinessID, err)
			} else {
				successCount++
				log.Printf("✅ 业务 %s 处理成功", businessRecord.BusinessID)
			}
		}

		// 销毁管理器
		ossManager.Destroy()
		log.Printf("🗑️ Worker %s OSS管理器已销毁", workerID)
	}

	log.Printf("🎉 OSS存储统计处理完成: 成功 %d, 失败 %d", successCount, failureCount)
}

// BusinessRecord 业务记录结构
type BusinessRecord struct {
	BusinessID string
	Record     models.DeployRecord
}

// groupRecordsByWorkerForBusiness 按Worker分组业务Records
func (s *Service) groupRecordsByWorkerForBusiness(businessIDToRecord map[string]models.DeployRecord) map[string][]BusinessRecord {
	workerGroups := make(map[string][]BusinessRecord)

	for businessID, record := range businessIDToRecord {
		workerID := record.WorkerID

		businessRecord := BusinessRecord{
			BusinessID: businessID,
			Record:     record,
		}

		workerGroups[workerID] = append(workerGroups[workerID], businessRecord)
	}

	return workerGroups
}

// processBusinessOSSStorage 处理单个业务的OSS存储统计
func (s *Service) processBusinessOSSStorage(ctx context.Context, businessRecord BusinessRecord, ossManager *TemporaryOSSManager) error {
	// 获取存储统计（包含存储量和文件数）
	ossResult, err := ossManager.GetOSSStats(businessRecord.Record.ServiceID)
	if err != nil {
		return fmt.Errorf("获取OSS存储统计失败: %w", err)
	}

	if !ossResult.Success {
		return fmt.Errorf("OSS存储检查失败: %s", ossResult.ErrorMessage)
	}

	// 提取存储和文件数据
	var diskUsage int64
	var fileCount int64

	if ossResult.OSSUtilStats != nil {
		diskUsage = int64(ossResult.OSSUtilStats.TotalSizeMB)
		fileCount = ossResult.OSSUtilStats.ObjectCount
	}

	// 直接使用 businessID 更新存储数据
	if err := s.updateOSSStorageDataWithBusinessID(businessRecord.BusinessID, diskUsage, fileCount, ossResult); err != nil {
		return fmt.Errorf("更新OSS存储数据失败: %w", err)
	}

	log.Printf("业务 %s OSS存储统计完成: 存储量=%dMB, 文件数=%d",
		businessRecord.BusinessID, diskUsage, fileCount)

	return nil
}

// updateOSSStorageDataWithBusinessID 直接基于 businessID 更新OSS存储相关数据
func (s *Service) updateOSSStorageDataWithBusinessID(businessID string, diskUsage int64, fileCount int64, ossResult *models.OSSStatusResult) error {
	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		// 数据库查询错误，返回错误
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("业务 %s 的资源使用记录不存在，创建新记录", businessID)

		newUsage := &models.ServiceResourceUsage{
			BusinessID:   businessID,
			OSSDiskUsage: diskUsage,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			OSSFileCount:   fileCount,
			CollectionTime: utils.GetCSTTime(),
			DataSources:    []string{"ossutil"},
		}

		// 添加OSS配置信息
		if ossResult.OSSConfig != nil {
			metadata.OSSBucket = ossResult.OSSConfig.Bucket
			metadata.OSSEndpoint = ossResult.OSSConfig.Endpoint
			metadata.OSSRegion = ossResult.OSSConfig.Region
			metadata.OSSFilePath = ossResult.OSSConfig.FilePath
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		now := utils.GetCSTTime()
		newUsage.LastAccessTime = &now

		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			log.Printf("创建业务 %s 的资源使用记录失败: %v", businessID, err)
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建业务 %s 的资源使用记录: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)
		return nil
	}

	// 记录存在，更新现有记录的存储数据
	log.Printf("业务 %s 的资源使用记录已存在，更新数据", businessID)

	existingUsage.OSSDiskUsage = diskUsage

	// 获取现有元数据
	metadata, err := existingUsage.GetMetadata()
	if err != nil {
		log.Printf("获取现有元数据失败，创建新的元数据: %v", err)
		metadata = &models.ResourceUsageMetadata{}
	}

	// 更新元数据
	metadata.OSSFileCount = fileCount
	metadata.CollectionTime = utils.GetCSTTime()
	if metadata.DataSources == nil {
		metadata.DataSources = []string{"ossutil"}
	}

	log.Printf("更新业务 %s 的OSS元数据: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)

	// 更新OSS配置信息
	if ossResult.OSSConfig != nil {
		metadata.OSSBucket = ossResult.OSSConfig.Bucket
		metadata.OSSEndpoint = ossResult.OSSConfig.Endpoint
		metadata.OSSRegion = ossResult.OSSConfig.Region
		metadata.OSSFilePath = ossResult.OSSConfig.FilePath
	}

	// 设置更新后的元数据
	if err := existingUsage.SetMetadata(metadata); err != nil {
		log.Printf("更新元数据失败: %v", err)
	} else {
		log.Printf("成功设置业务 %s 的元数据，JSON长度: %d", businessID, len(existingUsage.Metadata))
	}

	now := utils.GetCSTTime()
	existingUsage.LastAccessTime = &now

	if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
		log.Printf("更新业务 %s 的资源使用记录失败: %v", existingUsage.BusinessID, err)
		return fmt.Errorf("更新资源使用记录失败: %w", err)
	}

	log.Printf("成功更新业务 %s 的资源使用记录: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)
	return nil
}

// processOSSTrafficStats 处理OSS流量统计 - 🚀 基于 business_id 优化版本
// 🆕 使用新的临时OSS管理器架构
func (s *Service) processOSSTrafficStats(ctx context.Context, records []models.DeployRecord, t time.Time) {
	log.Printf("🚀 开始处理OSS流量统计")

	if len(records) == 0 {
		log.Printf("📭 没有需要处理的Records")
		return
	}

	// 🚀 优化：立即转换为 BusinessServiceMapping，避免重复处理
	businessMappings := s.buildBusinessServiceMappings(records)
	if len(businessMappings) == 0 {
		log.Printf("没有有效的业务映射，跳过OSS流量统计")
		return
	}

	log.Printf("🎯 开始处理 %d 个业务的OSS流量统计（来源：%d 个服务）", len(businessMappings), len(records))

	// 🚀 优化：基于 business_id 进行日志下载，避免重复下载
	buckets := make(map[string]bool, 5)
	for businessID, mapping := range businessMappings {
		// 1. 通过oss配置，读取oss日志目录中的日志文件清单
		//    a. t 参数是当前的时间，取 “年、月、日”，结合bucket 组合成过滤条件 --include=“*{bucket}year-month-day*”
		//    b. 拉取范围，前一天
		// 2. 读取 dbdata/oss_logs/文件夹文件，对比上一步拉取结果，做比对
		// 3. 下载dbdata/oss_logs/缺少的日志文件
		downloaded, skipped, err := s.processServiceOSSLogs(ctx, mapping.RepresentativeRecord, &buckets, t)
		if err != nil {
			log.Printf("处理业务OSS日志失败 (business: %s): %v", businessID, err)
			continue
		}

		log.Printf("业务日志文件处理完成: 下载 %d 个, 跳过 %d 个 (business: %s, 服务数: %d)",
			downloaded, skipped, businessID, len(mapping.ServiceRecords))
	}

	// 4. 通过文件统计流量，更新数据库
	// 🚀 优化：直接基于 business_id 处理流量统计
	if err := s.processBusinessTrafficStats(ctx, businessMappings, t); err != nil {
		log.Printf("处理业务流量统计失败: %v", err)
	}

	// 🧹 清理超过7天的OSS日志文件
	if err := s.cleanupOldOSSLogs(7); err != nil {
		log.Printf("⚠️  清理OSS日志文件失败: %v", err)
	}

	log.Printf("OSS流量统计处理完成")
}

// processBusinessTrafficStats 处理业务流量统计 - 🚀 完全基于 business_id 优化版本
// 直接基于 BusinessServiceMapping 进行流量统计，避免重复的 service_id → business_id 转换
func (s *Service) processBusinessTrafficStats(ctx context.Context, businessMappings map[string]*BusinessServiceMapping, t time.Time) error {
	log.Printf("🚀 开始处理 %d 个业务的流量统计", len(businessMappings))

	if len(businessMappings) == 0 {
		log.Printf("没有需要处理的业务映射")
		return nil
	}

	// 1. 批量查询业务资源使用记录，过滤出需要处理的业务
	businessIDs := make([]string, 0, len(businessMappings))
	for businessID := range businessMappings {
		businessIDs = append(businessIDs, businessID)
	}

	businessResourceUsageMap, err := s.resourceUsageRepo.GetByBusinessIDs(businessIDs)
	if err != nil {
		log.Printf("批量查询业务资源使用记录失败: %v，按全部需要处理", err)
		businessResourceUsageMap = make(map[string]*models.ServiceResourceUsage)
	}

	// 2. 过滤出需要处理的业务（跳过最近已更新的）
	businessesToProcess := make(map[string]*BusinessServiceMapping)
	skippedCount := 0

	for businessID, mapping := range businessMappings {
		usage := businessResourceUsageMap[businessID]

		// 判断是否需要处理（基于业务级别判断）
		if s.shouldSkipBusinessWithUsage(businessID, usage, t) {
			skippedCount++
			continue
		}

		businessesToProcess[businessID] = mapping
	}

	log.Printf("📊 过滤结果: 需要处理 %d 个业务, 跳过 %d 个业务", len(businessesToProcess), skippedCount)

	if len(businessesToProcess) == 0 {
		log.Printf("📋 所有业务都已处理过，无需重复处理")
		return nil
	}

	// 3. 🆕 使用新的临时OSS管理器架构处理流量统计
	businessTrafficMap, err := s.calculateAllBusinessTrafficForDate(ctx, businessesToProcess, t)
	if err != nil {
		return fmt.Errorf("计算业务流量失败: %w", err)
	}

	// 批量更新数据库
	successCount := 0
	var lastError error

	for businessID, trafficMB := range businessTrafficMap {
		select {
		case <-ctx.Done():
			return fmt.Errorf("上下文被取消，已处理 %d/%d 个业务", successCount, len(businessTrafficMap))
		default:
		}

		// 更新数据库
		if err := s.updateOSSTrafficDataWithBusinessID(businessID, trafficMB, 0); err != nil {
			log.Printf("更新业务 %s 的流量数据失败: %v", businessID, err)
			lastError = err
			continue
		}

		successCount++
		log.Printf("✅ 业务 %s 流量统计完成: %.2f MB", businessID, trafficMB)
	}

	log.Printf("🎉 业务流量统计处理完成: 成功 %d/%d 个业务", successCount, len(businessTrafficMap))

	if lastError != nil && successCount < len(businessTrafficMap) {
		return fmt.Errorf("部分业务更新失败，最后错误: %w", lastError)
	}

	return nil
}

// calculateAllBusinessTrafficForDate 🚀 完全基于 business_id 的流量统计
// 核心改进：直接基于 BusinessServiceMapping 进行处理，避免 service_id → business_id 转换
func (s *Service) calculateAllBusinessTrafficForDate(ctx context.Context, businessMappings map[string]*BusinessServiceMapping, t time.Time) (map[string]float64, error) {
	// 0. 🔧 修复时区问题：缓存当前处理时间，使用本地时区的日期截断
	processingDate := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	log.Printf("🕒 缓存处理时间: %s，避免长时间任务中时间变化", processingDate.Format("2006-01-02"))

	// 1. 🚀 预处理：为每个业务获取资源使用记录，构建处理所需的数据
	businessResourceMap := make(map[string]*models.ServiceResourceUsage)
	for businessID := range businessMappings {
		usage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
		if err != nil {
			log.Printf("⚠️  获取业务 %s 的资源记录失败: %v，跳过", businessID, err)
			continue
		}

		if usage == nil {
			// 🆕 记录不存在，创建默认记录
			log.Printf("📝 业务 %s 的资源记录不存在，创建默认记录用于流量计算", businessID)

			defaultUsage := s.createDefaultResourceUsageForTrafficCalculation(businessID)
			if err := s.resourceUsageRepo.Create(defaultUsage); err != nil {
				log.Printf("❌ 创建业务 %s 的默认资源记录失败: %v，跳过", businessID, err)
				continue
			}

			usage = defaultUsage
			log.Printf("✅ 业务 %s 的默认资源记录创建成功", businessID)
		}

		businessResourceMap[businessID] = usage
	}

	// 2. 🚀 预处理 NetworkTrafficUpdatedAt 为空的业务记录
	processedBusinessMap := s.preprocessBusinessWithNullTimestamp(businessResourceMap, t)

	// 3. 基于 business_id 和 NetworkTrafficUpdatedAt 进行分组
	groups := s.groupBusinessByStartDate(processedBusinessMap, processingDate)
	if len(groups) == 0 {
		log.Printf("📋 没有需要处理的业务分组")
		return make(map[string]float64), nil
	}

	log.Printf("📊 创建了 %d 个业务处理分组", len(groups))

	// 4. 从较早的时间组开始处理
	finalResults, err := s.processBusinessGroupsSequentially(ctx, groups, processingDate)
	if err != nil {
		return nil, fmt.Errorf("业务分组处理失败: %w", err)
	}

	log.Printf("🎉 业务流量统计处理完成: 共处理 %d 个业务的流量数据", len(finalResults))
	return finalResults, nil
}

// preprocessBusinessWithNullTimestamp 预处理 NetworkTrafficUpdatedAt 为空的业务记录
// 🚀 基于 business_id 的预处理，将空值设置为当前时间 -7天
func (s *Service) preprocessBusinessWithNullTimestamp(businessResourceMap map[string]*models.ServiceResourceUsage, currentTime time.Time) map[string]*models.ServiceResourceUsage {
	processedBusinessMap := make(map[string]*models.ServiceResourceUsage)

	// 计算默认时间：当前时间 -7天
	defaultTime := currentTime.AddDate(0, 0, -7)
	log.Printf("🔧 预处理：为空的 NetworkTrafficUpdatedAt 设置默认时间 %s", defaultTime.Format("2006-01-02 15:04:05"))

	nullCount := 0
	for businessID, usage := range businessResourceMap {
		// 复制记录
		processedUsage := *usage
		processedBusinessMap[businessID] = &processedUsage

		if usage.NetworkTrafficUpdatedAt == nil {
			// 设置为当前时间 -7天
			processedBusinessMap[businessID].NetworkTrafficUpdatedAt = &defaultTime
			log.Printf("📝 业务 %s: NetworkTrafficUpdatedAt 为空，设置为 %s",
				businessID, defaultTime.Format("2006-01-02 15:04:05"))
			nullCount++
		} else {
			log.Printf("📝 业务 %s: NetworkTrafficUpdatedAt = %s (无需修改)",
				businessID, usage.NetworkTrafficUpdatedAt.Format("2006-01-02 15:04:05"))
		}
	}

	log.Printf("✅ 预处理完成: 处理了 %d 个业务，其中 %d 个设置了默认时间", len(processedBusinessMap), nullCount)
	return processedBusinessMap
}

// groupBusinessByStartDate 基于 NetworkTrafficUpdatedAt 字段对业务进行分组
// 🚀 完全基于 business_id 的分组，返回按开始处理日期分组的业务集合
func (s *Service) groupBusinessByStartDate(businessResourceMap map[string]*models.ServiceResourceUsage, processingDate time.Time) map[string]*BusinessProcessingGroup {
	groups := make(map[string]*BusinessProcessingGroup)

	for businessID, usage := range businessResourceMap {
		// 经过预处理，所有业务都应该有 NetworkTrafficUpdatedAt 值
		if usage.NetworkTrafficUpdatedAt == nil {
			log.Printf("⚠️  警告：业务 %s 的 NetworkTrafficUpdatedAt 仍为空，跳过处理", businessID)
			continue
		}

		// 从上次更新的当天开始处理
		// NetworkTrafficUpdatedAt 表示已处理完前一天的日志，需要处理当天的日志
		// 🔧 修复时区问题：使用本地时区的日期截断
		lastUpdatedDate := time.Date(usage.NetworkTrafficUpdatedAt.Year(),
			usage.NetworkTrafficUpdatedAt.Month(),
			usage.NetworkTrafficUpdatedAt.Day(),
			0, 0, 0, 0, usage.NetworkTrafficUpdatedAt.Location())
		startDate := lastUpdatedDate // 不需要+1天，直接处理当天的日志
		log.Printf("📝 业务 %s: 从 %s 开始处理 (上次更新: %s)",
			businessID, startDate.Format("2006-01-02"), lastUpdatedDate.Format("2006-01-02"))

		// 确保开始日期不超过处理日期的前一天
		targetDate := processingDate.AddDate(0, 0, -1)
		if startDate.After(targetDate) {
			log.Printf("⏭️  业务 %s: 开始日期 %s 超出处理范围 %s，跳过",
				businessID, startDate.Format("2006-01-02"), targetDate.Format("2006-01-02"))
			continue
		}

		groupKey := startDate.Format("2006-01-02")
		if groups[groupKey] == nil {
			groups[groupKey] = &BusinessProcessingGroup{
				StartDate:   startDate,
				BusinessIDs: []string{},
			}
			log.Printf("📁 创建新业务分组: %s", groupKey)
		}
		groups[groupKey].BusinessIDs = append(groups[groupKey].BusinessIDs, businessID)
	}

	// 输出分组统计
	for groupKey, group := range groups {
		log.Printf("📊 业务分组 %s: %d 个业务", groupKey, len(group.BusinessIDs))
	}

	return groups
}

// processBusinessGroupsSequentially 按时间顺序处理业务分组，支持分组间的业务迁移
// 🚀 完全基于 business_id 的分组处理，从较早的时间组开始处理
func (s *Service) processBusinessGroupsSequentially(ctx context.Context, groups map[string]*BusinessProcessingGroup, processingDate time.Time) (map[string]float64, error) {
	// 按日期排序分组键
	var sortedKeys []string
	for key := range groups {
		sortedKeys = append(sortedKeys, key)
	}
	sort.Strings(sortedKeys) // 从早到晚排序

	log.Printf("🔄 开始按时间顺序处理 %d 个业务分组: %v", len(sortedKeys), sortedKeys)

	// 全局业务流量累加器
	globalAccumulator := &BusinessTrafficAccumulator{
		totalTraffic:       make(map[string]float64),
		previousDayTraffic: make(map[string]float64),
	}

	// 按顺序处理每个分组
	for _, groupKey := range sortedKeys {
		group := groups[groupKey]
		if len(group.BusinessIDs) == 0 {
			log.Printf("📋 业务分组 %s 为空，跳过", groupKey)
			continue
		}

		log.Printf("🚀 开始处理业务分组 %s: %d 个业务", groupKey, len(group.BusinessIDs))

		// 处理该分组
		if err := s.processBusinessGroup(ctx, group, processingDate, groups, globalAccumulator); err != nil {
			return nil, fmt.Errorf("处理业务分组 %s 失败: %w", groupKey, err)
		}

		log.Printf("✅ 业务分组 %s 处理完成", groupKey)
	}

	// 🚀 关键改进：全部分组处理完后，统一更新数据库
	if err := s.batchUpdateBusinessDatabase(globalAccumulator, processingDate); err != nil {
		return nil, fmt.Errorf("批量更新业务数据库失败: %w", err)
	}

	// 返回最终结果
	globalAccumulator.mu.RLock()
	result := make(map[string]float64)
	for businessID, traffic := range globalAccumulator.totalTraffic {
		result[businessID] = traffic
	}
	globalAccumulator.mu.RUnlock()

	return result, nil
}

// processBusinessGroup 处理单个业务分组，支持多天日志处理和分组间迁移
// 🚀 完全基于 business_id 的分组处理逻辑，实现了时间判断和分组迁移机制
func (s *Service) processBusinessGroup(ctx context.Context, group *BusinessProcessingGroup, processingDate time.Time, allGroups map[string]*BusinessProcessingGroup, globalAccumulator *BusinessTrafficAccumulator) error {
	currentLogDate := group.StartDate
	targetDate := processingDate.AddDate(0, 0, -1) // 目标：处理到前一天

	log.Printf("📅 业务分组处理范围: %s 到 %s", currentLogDate.Format("2006-01-02"), targetDate.Format("2006-01-02"))

	// 处理该分组需要的所有日期
	for !currentLogDate.After(targetDate) {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return fmt.Errorf("上下文被取消")
		default:
		}

		dateStr := currentLogDate.Format("2006-01-02")
		log.Printf("📄 处理日期: %s 的日志", dateStr)

		// 获取该日期的所有日志文件
		logFiles, err := s.findAllLogFilesForDate(dateStr)
		if err != nil {
			log.Printf("⚠️  查找 %s 的日志文件失败: %v", dateStr, err)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		if len(logFiles) == 0 {
			log.Printf("📭 %s 没有找到日志文件", dateStr)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		log.Printf("📂 %s 找到 %d 个日志文件", dateStr, len(logFiles))

		// 🚀 为该组所有业务计算当天的流量（直接基于 business_id）
		dayTraffic, err := s.calculateDayBusinessTrafficForGroup(group.BusinessIDs, logFiles)
		if err != nil {
			log.Printf("⚠️  计算 %s 业务流量失败: %v", dateStr, err)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		// 累加到全局累加器
		// 计算前一天日期
		previousDate := processingDate.AddDate(0, 0, -1).Format("2006-01-02")
		currentDateStr := currentLogDate.Format("2006-01-02")
		s.accumulateBusinessTraffic(globalAccumulator, currentDateStr, previousDate, dayTraffic)

		// 判断下一步处理逻辑
		nextLogDate := currentLogDate.AddDate(0, 0, 1)
		if nextLogDate.Before(processingDate) {
			// 情况1: 下个日期 < 当前处理日期，需要继续处理，移动到下个分组
			log.Printf("🔄 需要继续处理，移动到下个分组 (下个日期: %s < 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
			s.moveBusinessToNextGroup(group.BusinessIDs, nextLogDate, allGroups, processingDate)
		} else if nextLogDate.Equal(processingDate) {
			// 情况2: 下个日期 == 当前处理日期，处理完成
			log.Printf("✅ 处理完成 (下个日期: %s == 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
			// 记录完成状态（实际更新在批量更新中进行）
			s.updateDatabaseForBusiness(group.BusinessIDs, globalAccumulator, currentLogDate)
		} else {
			// 情况3: 下个日期 > 当前处理日期，这种情况不应该出现
			log.Printf("⚠️  异常情况: 下个日期超出处理范围 (下个日期: %s > 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
		}

		currentLogDate = nextLogDate
	}

	return nil
}

// calculateDayBusinessTrafficForGroup 计算指定业务分组在指定日期的流量
// 🚀 完全基于 business_id 的流量计算，每个日志文件只解析一次
// ✅ 已使用智能Provider检测，支持多厂商OSS
func (s *Service) calculateDayBusinessTrafficForGroup(businessIDs []string, logFiles []string) (map[string]float64, error) {
	dayTraffic := make(map[string]float64) // businessID -> traffic

	log.Printf("📊 需要统计 %d 个业务的流量", len(businessIDs))

	// 🆕 智能Provider检测 - 根据日志文件路径或尝试多个Provider
	providers := []OSSProvider{
		&utils.AliOSSProvider{}, // 优先尝试阿里云
		&utils.HSTOSProvider{},  // 然后尝试火山引擎
	}

	// 遍历所有日志文件
	for _, logFile := range logFiles {
		// 尝试多个Provider解析日志文件
		var entries []*models.OSSLogEntry
		var parseErr error

		for _, provider := range providers {
			entries, parseErr = provider.ParseLogFile(logFile)
			if parseErr == nil {
				// 解析成功，跳出Provider循环
				break
			}
		}

		if parseErr != nil {
			log.Printf("⚠️  所有Provider都无法解析日志文件 %s: %v", filepath.Base(logFile), parseErr)
			continue
		}

		log.Printf("📄 文件 %s: 解析了 %d 条日志记录", filepath.Base(logFile), len(entries))

		// 直接基于 businessID 统计流量
		fileBusinessTraffic := make(map[string]int64)
		for _, businessID := range businessIDs {
			// 在当前日志文件的entries中查找该业务的流量
			for _, entry := range entries {
				if s.isBusinessTrafficMatch(entry.FilePath, businessID) {
					fileBusinessTraffic[businessID] += entry.ObjectSize
				}
			}
		}

		// 转换为MB并累加到日流量
		for businessID, bytes := range fileBusinessTraffic {
			trafficMB := float64(bytes) / (1024 * 1024)
			dayTraffic[businessID] += trafficMB
			if trafficMB > 0 {
				log.Printf("  ✅ 业务 %s: +%.2f MB", businessID, trafficMB)
			}
		}
	}

	return dayTraffic, nil
}

// accumulateBusinessTraffic 将日流量累加到全局累加器，支持前一天流量记录
// 🚀 完全基于 business_id 的累加逻辑
func (s *Service) accumulateBusinessTraffic(accumulator *BusinessTrafficAccumulator, currentDate, previousDate string, dayTraffic map[string]float64) {
	accumulator.mu.Lock()
	defer accumulator.mu.Unlock()

	// 确保 map 已初始化
	if accumulator.totalTraffic == nil {
		accumulator.totalTraffic = make(map[string]float64)
	}
	if accumulator.previousDayTraffic == nil {
		accumulator.previousDayTraffic = make(map[string]float64)
	}

	// dayTraffic 是 businessID -> traffic
	for businessID, traffic := range dayTraffic {
		// 累加总流量
		accumulator.totalTraffic[businessID] += traffic

		// 如果当前处理的是前一天的数据，记录到 previousDayTraffic
		if currentDate == previousDate {
			accumulator.previousDayTraffic[businessID] = traffic
		}

		log.Printf("📊 累加业务 %s: +%.2f MB (总计: %.2f MB)",
			businessID, traffic, accumulator.totalTraffic[businessID])
	}
}

// moveBusinessToNextGroup 将业务移动到下一个分组
// 🚀 完全基于 business_id 的记录迁移，实现分组间的业务迁移，支持多天日志回溯
func (s *Service) moveBusinessToNextGroup(businessIDs []string, nextLogDate time.Time, allGroups map[string]*BusinessProcessingGroup, processingDate time.Time) {
	// 🔑 关键判断：只有在需要继续处理时才创建下个分组
	if nextLogDate.Before(processingDate) {
		nextGroupKey := nextLogDate.Format("2006-01-02")

		// 如果下个分组不存在，创建它
		if allGroups[nextGroupKey] == nil {
			allGroups[nextGroupKey] = &BusinessProcessingGroup{
				StartDate:   nextLogDate,
				BusinessIDs: []string{},
			}
			log.Printf("📝 创建新业务分组: %s (条件: %s < %s)",
				nextGroupKey,
				nextLogDate.Format("2006-01-02"),
				processingDate.Format("2006-01-02"))
		}

		// 将业务移动到下个分组
		allGroups[nextGroupKey].BusinessIDs = append(allGroups[nextGroupKey].BusinessIDs, businessIDs...)
		log.Printf("🔄 移动 %d 个业务到分组 %s", len(businessIDs), nextGroupKey)

	} else {
		// 已经处理到最后一天，不需要创建新分组
		log.Printf("✅ 业务处理完成，无需创建新分组 (下个日期: %s >= 处理日期: %s)",
			nextLogDate.Format("2006-01-02"),
			processingDate.Format("2006-01-02"))
	}
}

// updateDatabaseForBusiness 为业务记录完成状态（实际更新在批量更新中进行）
// 🚀 基于 business_id 的数据库更新标记
func (s *Service) updateDatabaseForBusiness(businessIDs []string, globalAccumulator *BusinessTrafficAccumulator, currentLogDate time.Time) {
	// 这个方法主要用于标记处理完成状态
	// 实际的数据库更新在 batchUpdateBusinessDatabase 中统一进行
	log.Printf("📝 标记 %d 个业务的处理完成状态 (日期: %s)", len(businessIDs), currentLogDate.Format("2006-01-02"))

	for _, businessID := range businessIDs {
		log.Printf("✅ 业务 %s: 处理完成标记", businessID)
	}
}

// batchUpdateBusinessDatabase 批量更新业务数据库
// 🚀 完全基于 business_id 的批量数据库更新
func (s *Service) batchUpdateBusinessDatabase(accumulator *BusinessTrafficAccumulator, processingDate time.Time) error {
	accumulator.mu.RLock()
	defer accumulator.mu.RUnlock()

	if len(accumulator.totalTraffic) == 0 {
		log.Printf("📋 没有需要更新的业务流量数据")
		return nil
	}

	log.Printf("🚀 开始批量更新 %d 个业务的流量数据", len(accumulator.totalTraffic))

	successCount := 0
	var lastError error

	for businessID, totalTraffic := range accumulator.totalTraffic {
		// 获取前一天流量（如果有）
		previousDayTraffic := accumulator.previousDayTraffic[businessID]

		// 🚀 直接基于 business_id 更新，无需转换
		if err := s.updateOSSTrafficDataWithBusinessID(businessID, totalTraffic, previousDayTraffic); err != nil {
			lastError = err
			log.Printf("❌ 更新业务 %s 的流量数据失败: %v", businessID, err)
			continue
		}

		log.Printf("✅ 业务 %s 流量数据更新成功: 总流量 %.2f MB, 前一天流量 %.2f MB",
			businessID, totalTraffic, previousDayTraffic)
		successCount++
	}

	if successCount == len(accumulator.totalTraffic) {
		log.Printf("🎉 批量更新完成: 成功更新 %d 个业务的流量数据", successCount)
	} else {
		log.Printf("⚠️  批量更新部分完成: 成功更新 %d/%d 个业务", successCount, len(accumulator.totalTraffic))
		if lastError != nil {
			return fmt.Errorf("部分业务更新失败，最后错误: %w", lastError)
		}
	}

	return nil
}

// processServiceOSSLogs 处理单个服务的OSS日志文件下载
// 实现注释中的三个步骤：1.获取远程文件列表 2.对比本地文件 3.下载缺失文件
// 返回值：(下载文件数, 跳过文件数, 错误)
func (s *Service) processServiceOSSLogs(ctx context.Context, record models.DeployRecord, bucketCache *map[string]bool, t time.Time) (int, int, error) {
	// 🆕 使用新的临时OSS管理器处理日志下载

	// 1. 检查Worker缓存，避免重复处理
	if (*bucketCache)[record.WorkerID] {
		return 0, 1, nil
	} else {
		(*bucketCache)[record.WorkerID] = true
	}

	// 2. 为该Record创建临时OSS管理器
	ossManager, err := CreateOSSManagerForWorker(record.WorkerID, record)
	if err != nil {
		return 0, 0, fmt.Errorf("创建OSS管理器失败: %w", err)
	}
	defer ossManager.Destroy() // 确保资源释放

	// 3. 定义要检查的日期范围：前一天
	currentDate := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	previousDate := currentDate.AddDate(0, 0, -1)
	datesToCheck := []time.Time{previousDate}

	log.Printf("🕐 日期计算: 输入时间=%s, 当前日期=%s, 前一天=%s",
		t.Format("2006-01-02 15:04:05 MST"),
		currentDate.Format("2006-01-02 15:04:05 MST"),
		previousDate.Format("2006-01-02 15:04:05 MST"))

	var totalDownloaded, totalSkipped int

	// 4. 遍历每个日期，获取远程文件列表并下载缺失文件
	for _, date := range datesToCheck {
		log.Printf("检查服务 %s 在 %s 的日志文件", record.ServiceID, date.Format("2006-01-02"))

		// 使用临时管理器的Provider获取日志文件列表
		filesToDownload, err := ossManager.provider.ListLogFiles(ossManager.config, date)
		if err != nil {
			log.Printf("获取服务 %s 在 %s 的日志文件列表失败: %v",
				record.ServiceID, date.Format("2006-01-02"), err)
			continue // 继续处理下一个日期
		}

		log.Printf("服务 %s 在 %s 需要下载 %d 个日志文件",
			record.ServiceID, date.Format("2006-01-02"), len(filesToDownload))

		// 下载日志文件
		downloaded, skipped := s.downloadOSSLogFilesWithManager(ctx, ossManager, filesToDownload, record.ServiceID, date)
		totalDownloaded += downloaded
		totalSkipped += skipped
	}

	return totalDownloaded, totalSkipped, nil
}

// downloadOSSLogFilesWithManager 使用临时OSS管理器下载日志文件
// 返回值：(下载文件数, 跳过文件数)
func (s *Service) downloadOSSLogFilesWithManager(ctx context.Context, ossManager *TemporaryOSSManager, filesToDownload []string, serviceID string, date time.Time) (int, int) {
	if len(filesToDownload) == 0 {
		log.Printf("服务 %s 在 %s 没有需要下载的文件", serviceID, date.Format("2006-01-02"))
		return 0, 0
	}

	var downloaded, skipped int

	for _, logFile := range filesToDownload {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			log.Printf("上下文被取消，停止下载服务 %s 的日志文件", serviceID)
			return downloaded, skipped
		default:
		}

		log.Printf("开始下载服务 %s 的日志文件: %s", serviceID, logFile)

		// 使用临时管理器的Provider下载文件
		localPath, err := ossManager.provider.DownloadLogFile(ossManager.config, logFile)
		if err != nil {
			log.Printf("下载服务 %s 的日志文件 %s 失败: %v", serviceID, logFile, err)
			skipped++
			continue
		}

		downloaded++
		log.Printf("成功下载服务 %s 的日志文件: %s -> %s", serviceID, logFile, localPath)
	}

	log.Printf("服务 %s 在 %s 的文件下载完成: 成功 %d 个, 跳过 %d 个",
		serviceID, date.Format("2006-01-02"), downloaded, skipped)

	return downloaded, skipped
}

// cleanupOldOSSLogs 清理超过指定天数的OSS日志文件
// 默认清理超过7天的日志文件，避免磁盘空间占用过多
func (s *Service) cleanupOldOSSLogs(maxDays int) error {
	if maxDays <= 0 {
		maxDays = 7 // 默认保留7天
	}

	logDir := "./dbdata/oss_logs"

	// 检查目录是否存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		log.Printf("📁 OSS日志目录不存在，无需清理: %s", logDir)
		return nil
	}

	// 计算截止时间：当前时间 - maxDays 天 (使用东八区时间)
	cutoffTime := utils.GetCSTTime().AddDate(0, 0, -maxDays)
	log.Printf("🧹 开始清理OSS日志文件: 删除 %s 之前的文件", cutoffTime.Format("2006-01-02"))

	// 读取目录中的所有文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return fmt.Errorf("读取OSS日志目录失败: %w", err)
	}

	var deletedCount, skippedCount int
	var totalSize int64

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(logDir, file.Name())

		// 获取文件信息
		fileInfo, err := file.Info()
		if err != nil {
			log.Printf("⚠️  获取文件信息失败: %s, 错误: %v", file.Name(), err)
			skippedCount++
			continue
		}

		// 检查文件修改时间
		if fileInfo.ModTime().Before(cutoffTime) {
			// 文件超过保留期限，删除
			fileSize := fileInfo.Size()
			if err := os.Remove(filePath); err != nil {
				log.Printf("❌ 删除文件失败: %s, 错误: %v", file.Name(), err)
				skippedCount++
				continue
			}

			log.Printf("🗑️  删除过期日志文件: %s (大小: %.2f MB, 修改时间: %s)",
				file.Name(), float64(fileSize)/(1024*1024), fileInfo.ModTime().Format("2006-01-02 15:04:05"))
			deletedCount++
			totalSize += fileSize
		} else {
			// 文件在保留期限内，跳过
			log.Printf("📄 保留日志文件: %s (修改时间: %s)",
				file.Name(), fileInfo.ModTime().Format("2006-01-02 15:04:05"))
		}
	}

	log.Printf("🎉 OSS日志清理完成: 删除 %d 个文件 (%.2f MB), 跳过 %d 个文件",
		deletedCount, float64(totalSize)/(1024*1024), skippedCount)

	return nil
}

// updateHardwareResourceAllocation 更新硬件资源分配情况
// 遍历RUNNING状态的记录，按worker_id和node_ip分组计算资源使用量，更新到对应的worker节点中
// 🔑 关键功能：如果 runningRecords 为空，会清零所有节点的资源分配
func (s *Service) updateHardwareResourceAllocation(runningRecords []models.DeployRecord) {
	if len(runningRecords) == 0 {
		log.Printf("没有运行中的服务，开始清零所有节点的资源分配")
		// 🔑 清零所有节点的资源分配
		s.clearAllNodeResourceAllocations()
		return
	}

	log.Printf("Found %d RUNNING or PROCESSING services for hardware resource calculation", len(runningRecords))

	// 统计各状态的服务数量，便于调试
	statusCount := make(map[string]int)
	for _, record := range runningRecords {
		statusCount[record.Status]++
	}
	log.Printf("Service status breakdown: %+v", statusCount)

	// 🆕 使用统一的资源分配更新方法，避免数据库写入覆盖
	if err := s.updateAllNodeResourceAllocations(runningRecords); err != nil {
		log.Printf("Failed to update node resource allocations: %v", err)

		// 发送硬件资源分配更新失败通知
		s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelError,
			Title:     fmt.Sprintf("[S:%s] 硬件资源分配更新失败", s.GetServiceIP()),
			Content:   fmt.Sprintf("统一资源分配更新失败: %v, 涉及服务数: %d", err, len(runningRecords)),
			ServiceID: "",
			Metadata: map[string]interface{}{
				"service_count": len(runningRecords),
				"error_type":    "unified_resource_allocation_update_failed",
			},
			Timestamp: utils.GetCSTTimeString(),
		})
	}

	log.Printf("Completed hardware resource allocation update")
}

// updateAllNodeResourceAllocations 统一更新所有节点的资源分配
// 一次性处理有服务和无服务的节点，避免数据库写入覆盖
func (s *Service) updateAllNodeResourceAllocations(runningRecords []models.DeployRecord) error {
	log.Printf("开始统一更新所有节点的资源分配...")

	// 1. 一次性获取所有Worker数据
	workers, err := s.workerRepo.GetAll(nil)
	if err != nil {
		log.Printf("获取所有worker失败: %v", err)
		return fmt.Errorf("failed to get all workers: %w", err)
	}

	// 2. 构建运行服务的节点映射
	usedNodes := s.buildUsedNodesMap(runningRecords)
	log.Printf("构建节点映射完成: %d 个节点有运行服务", len(usedNodes))

	// 3. 在内存中同时处理所有节点
	totalNodes := 0
	for _, worker := range workers {
		totalNodes += len(worker.Nodes)
	}
	log.Printf("开始处理 %d 个worker的 %d 个节点", len(workers), totalNodes)
	updatedWorkers := s.processAllNodesInMemory(workers, usedNodes)

	// 4. 批量更新数据库
	if err := s.batchUpdateWorkers(updatedWorkers); err != nil {
		return fmt.Errorf("批量更新worker失败: %w", err)
	}

	log.Printf("统一资源分配更新完成: 更新了 %d 个worker", len(updatedWorkers))
	return nil
}

// buildUsedNodesMap 构建运行服务的节点映射
func (s *Service) buildUsedNodesMap(runningRecords []models.DeployRecord) map[string][]models.DeployRecord {
	usedNodes := make(map[string][]models.DeployRecord)

	for _, record := range runningRecords {
		// 跳过没有worker_id或node_ip的记录
		if record.WorkerID == "" || record.NodeIP == "" {
			log.Printf("跳过服务 %s: 缺少WorkerID或NodeIP", record.ServiceID)
			continue
		}

		// 创建节点key
		nodeKey := fmt.Sprintf("%s:%s", record.WorkerID, record.NodeIP)
		usedNodes[nodeKey] = append(usedNodes[nodeKey], record)
	}

	return usedNodes
}

// processAllNodesInMemory 在内存中同时处理所有节点的资源分配
func (s *Service) processAllNodesInMemory(workers []models.WorkerInfo, usedNodes map[string][]models.DeployRecord) []models.WorkerInfo {
	var updatedWorkers []models.WorkerInfo

	for _, worker := range workers {
		workerUpdated := false
		nodesWithServices := 0
		nodesClearedCount := 0
		nodesUpdatedCount := 0

		// 检查该worker的每个节点
		for i := range worker.Nodes {
			node := &worker.Nodes[i]
			nodeKey := fmt.Sprintf("%s:%s", worker.WorkerID, node.IP)

			if records, hasServices := usedNodes[nodeKey]; hasServices {
				nodesWithServices++
				// 有服务的节点：重新计算分配
				newAllocation := s.calculateNodeAllocation(records, node.Reserve_rate)

				if node.Cpu_allocation != newAllocation.CPU || node.Memory_allocation != newAllocation.Memory {
					node.Cpu_allocation = newAllocation.CPU
					node.Memory_allocation = newAllocation.Memory
					workerUpdated = true
					nodesUpdatedCount++
				}

				// 🆕 检查节点资源使用率并发送告警通知
				s.checkNodeResourceUsage(&worker, i)
			} else {
				// 无服务的节点：清零分配
				if node.Cpu_allocation > 0 || node.Memory_allocation > 0 || node.Disk_allocation > 0 {
					node.Cpu_allocation = 0
					node.Memory_allocation = 0
					node.Disk_allocation = 0
					workerUpdated = true
					nodesClearedCount++
				}
			}
		}

		// 输出Worker级别的汇总日志
		if workerUpdated {
			log.Printf("Worker %s: %d个节点有服务, %d个节点已更新, %d个节点已清零",
				worker.WorkerID, nodesWithServices, nodesUpdatedCount, nodesClearedCount)
			updatedWorkers = append(updatedWorkers, worker)
		} else if nodesWithServices > 0 {
			// 有服务但无需更新的情况也记录一下
			log.Printf("Worker %s: %d个节点有服务, 资源分配无变化",
				worker.WorkerID, nodesWithServices)
		}
	}

	return updatedWorkers
}

// NodeAllocation 节点资源分配结构
type NodeAllocation struct {
	CPU    float64
	Memory int
}

// calculateNodeAllocation 计算节点的资源分配
func (s *Service) calculateNodeAllocation(records []models.DeployRecord, reserveRate float64) NodeAllocation {
	var totalCPU float64
	var totalMemory float64

	// 计算该节点上所有服务的总资源使用量
	for _, record := range records {
		serviceCPU := record.ApiCpu * float64(record.ApiReplica)
		serviceMemory := float64(record.ApiMemory) * float64(record.ApiReplica)

		totalCPU += serviceCPU
		totalMemory += serviceMemory
	}

	// 应用预留率计算实际分配
	allocatedCPU := totalCPU * reserveRate
	allocatedMemory := int(totalMemory * reserveRate)

	return NodeAllocation{
		CPU:    allocatedCPU,
		Memory: allocatedMemory,
	}
}

// batchUpdateWorkers 批量更新Worker信息到数据库
func (s *Service) batchUpdateWorkers(workers []models.WorkerInfo) error {
	if len(workers) == 0 {
		log.Printf("没有需要更新的worker")
		return nil
	}

	successCount := 0
	for _, worker := range workers {
		if err := s.workerRepo.Update(worker); err != nil {
			log.Printf("❌ 更新worker %s 失败: %v", worker.WorkerID, err)
			return fmt.Errorf("failed to update worker %s: %w", worker.WorkerID, err)
		}
		successCount++
	}

	log.Printf("✅ 批量更新完成: 成功更新 %d 个worker", successCount)
	return nil
}

// clearAllNodeResourceAllocations 清零所有节点的资源分配
// 当没有运行中的服务时，需要将所有节点的资源分配清零，避免"幽灵资源占用"
func (s *Service) clearAllNodeResourceAllocations() {
	// 🔒 添加数据库操作锁，避免与其他定时任务的数据库冲突
	s.dbOperationMutex.Lock()
	defer s.dbOperationMutex.Unlock()

	log.Printf("开始清零所有节点的资源分配...")

	// 获取所有 worker
	workers, err := s.workerRepo.GetAll(nil)
	if err != nil {
		log.Printf("获取 worker 列表失败，无法清零资源分配: %v", err)

		// 🆕 发送获取worker列表失败通知
		s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelError,
			Title:     fmt.Sprintf("[S:%s] 硬件资源清零失败", s.GetServiceIP()),
			Content:   fmt.Sprintf("获取worker列表失败，无法清零资源分配: %v", err),
			ServiceID: "",
			Metadata: map[string]interface{}{
				"error_type": "get_workers_failed_for_resource_clear",
			},
			Timestamp: utils.GetCSTTimeString(),
		})
		return
	}

	if len(workers) == 0 {
		log.Printf("没有找到任何 worker，跳过资源分配清零")
		return
	}

	log.Printf("找到 %d 个 worker，开始清零资源分配", len(workers))

	clearedNodes := 0
	for _, worker := range workers {
		// 检查是否需要清零
		needUpdate := false
		for i := range worker.Nodes {
			if worker.Nodes[i].Cpu_allocation > 0 || worker.Nodes[i].Memory_allocation > 0 || worker.Nodes[i].Disk_allocation > 0 {
				needUpdate = true
				break
			}
		}

		if !needUpdate {
			log.Printf("Worker %s 的所有节点资源分配已为零，跳过", worker.WorkerID)
			continue
		}

		// 🔑 数据库操作已在 clearAllNodeResourceAllocations 方法中通过 dbOperationMutex 保护
		log.Printf("🔒 Database operations protected by dbOperationMutex, proceeding with resource clear for worker %s", worker.WorkerID)

		// 重新获取最新的 worker 信息
		freshWorker, err := s.workerRepo.GetByID(worker.WorkerID)
		if err != nil {
			log.Printf("获取 worker %s 最新信息失败: %v", worker.WorkerID, err)

			// 🆕 发送获取worker信息失败通知
			s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelError,
				Title:     fmt.Sprintf("[S:%s] 硬件资源清零失败", s.GetServiceIP()),
				Content:   fmt.Sprintf("获取worker %s 最新信息失败，无法清零资源分配: %v", worker.WorkerID, err),
				ServiceID: "",
				Metadata: map[string]interface{}{
					"worker_id":  worker.WorkerID,
					"error_type": "get_worker_info_failed_for_resource_clear",
				},
				Timestamp: utils.GetCSTTimeString(),
			})
			continue
		}

		// 清零所有节点的资源分配
		for i := range freshWorker.Nodes {
			if freshWorker.Nodes[i].Cpu_allocation > 0 || freshWorker.Nodes[i].Memory_allocation > 0 || freshWorker.Nodes[i].Disk_allocation > 0 {
				log.Printf("清零节点 %s 资源分配: CPU=%.2f→0, Memory=%d→0, Disk=%d→0",
					freshWorker.Nodes[i].IP,
					freshWorker.Nodes[i].Cpu_allocation,
					freshWorker.Nodes[i].Memory_allocation,
					freshWorker.Nodes[i].Disk_allocation)

				freshWorker.Nodes[i].Cpu_allocation = 0
				freshWorker.Nodes[i].Memory_allocation = 0
				freshWorker.Nodes[i].Disk_allocation = 0
				clearedNodes++
			}
		}

		// 更新数据库
		if err := s.workerRepo.Update(freshWorker); err != nil {
			log.Printf("更新 worker %s 资源分配失败: %v", worker.WorkerID, err)

			// 🆕 发送数据库更新失败通知
			s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelError,
				Title:     fmt.Sprintf("[S:%s] 硬件资源清零失败", s.GetServiceIP()),
				Content:   fmt.Sprintf("更新worker %s 资源分配到数据库失败: %v", worker.WorkerID, err),
				ServiceID: "",
				Metadata: map[string]interface{}{
					"worker_id":  worker.WorkerID,
					"error_type": "update_worker_resource_clear_failed",
				},
				Timestamp: utils.GetCSTTimeString(),
			})
		} else {
			log.Printf("成功清零 worker %s 的资源分配", worker.WorkerID)
		}
	}

	log.Printf("资源分配清零完成，共处理了 %d 个节点", clearedNodes)
}

// updateOSSTrafficDataWithBusinessID 直接基于 businessID 更新OSS流量数据到数据库
func (s *Service) updateOSSTrafficDataWithBusinessID(businessID string, totalTrafficMB, previousDayTrafficMB float64) error {
	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	currentTime := utils.GetCSTTime()

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("业务 %s 的资源使用记录不存在，创建新记录", businessID)

		newUsage := &models.ServiceResourceUsage{
			BusinessID:              businessID,
			OSSNetworkTraffic:       totalTrafficMB,
			OSSNetworkTrafficByDay:  previousDayTrafficMB,
			NetworkTrafficUpdatedAt: &currentTime,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			CollectionTime: currentTime,
			DataSources:    []string{"oss_logs"},
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 创建新记录
		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建业务 %s 的资源使用记录: 总流量=%.2fMB, 前一天=%.2fMB",
			businessID, totalTrafficMB, previousDayTrafficMB)
	} else {
		// 记录存在，累加流量数据（增量计算）
		oldTraffic := existingUsage.OSSNetworkTraffic
		newTraffic := oldTraffic + totalTrafficMB
		log.Printf("累加业务 %s 的OSS流量数据: 原值=%.2fMB, 增量=+%.2fMB, 新值=%.2fMB",
			businessID, oldTraffic, totalTrafficMB, newTraffic)

		// 🚀 关键：累加流量数据而不是覆盖，同时设置前一天流量
		existingUsage.OSSNetworkTraffic = newTraffic
		existingUsage.OSSNetworkTrafficByDay = previousDayTrafficMB
		existingUsage.NetworkTrafficUpdatedAt = &currentTime

		// 更新元数据
		metadata, err := existingUsage.GetMetadata()
		if err != nil {
			log.Printf("获取元数据失败: %v", err)
			metadata = &models.ResourceUsageMetadata{}
		}

		// 更新数据源和收集时间
		metadata.CollectionTime = currentTime
		if metadata.DataSources == nil {
			metadata.DataSources = []string{"oss_logs"}
		} else {
			// 检查是否已包含 oss_logs
			found := false
			for _, source := range metadata.DataSources {
				if source == "oss_logs" {
					found = true
					break
				}
			}
			if !found {
				metadata.DataSources = append(metadata.DataSources, "oss_logs")
			}
		}

		// 设置更新后的元数据
		if err := existingUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 更新记录
		if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
			return fmt.Errorf("更新资源使用记录失败: %w", err)
		}

		log.Printf("成功更新业务 %s 的OSS流量数据: 总流量增量=%.2fMB, 前一天=%.2fMB",
			businessID, totalTrafficMB, previousDayTrafficMB)
	}

	return nil
}

// findAllLogFilesForDate 查找指定日期的所有日志文件
// 🚀 动态发现所有 *_logs 目录，支持多厂商OSS日志
func (s *Service) findAllLogFilesForDate(dateStr string) ([]string, error) {
	baseDir := "./dbdata"

	// 检查基础目录是否存在
	if _, err := os.Stat(baseDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("基础目录不存在: %s", baseDir)
	}

	// 读取基础目录，查找所有 *_logs 目录
	entries, err := os.ReadDir(baseDir)
	if err != nil {
		return nil, fmt.Errorf("读取基础目录失败: %w", err)
	}

	var allMatchingFiles []string
	logDirsFound := 0
	var totalSizeBytes int64

	// 扫描所有符合命名约定的日志目录
	for _, entry := range entries {
		if !entry.IsDir() || !strings.HasSuffix(entry.Name(), "_logs") {
			continue
		}

		logDir := filepath.Join(baseDir, entry.Name())
		logDirsFound++

		log.Printf("🔍 扫描日志目录: %s", logDir)

		// 在该目录中查找指定日期的文件
		files, err := s.findLogFilesInDirectory(logDir, dateStr)
		if err != nil {
			log.Printf("⚠️  扫描目录 %s 失败: %v", logDir, err)
			continue
		}

		if len(files) > 0 {
			// 计算该目录下文件的总大小
			dirSizeBytes := s.calculateFilesSize(files)
			totalSizeBytes += dirSizeBytes

			log.Printf("📂 目录 %s 找到 %d 个匹配文件 (%.2f MB)",
				entry.Name(), len(files), float64(dirSizeBytes)/(1024*1024))
			allMatchingFiles = append(allMatchingFiles, files...)
		}
	}

	// 转换为MB并输出详细统计
	totalSizeMB := float64(totalSizeBytes) / (1024 * 1024)
	log.Printf("📊 总计扫描 %d 个日志目录，找到 %d 个匹配文件，总大小: %.2f MB",
		logDirsFound, len(allMatchingFiles), totalSizeMB)
	return allMatchingFiles, nil
}

// findLogFilesInDirectory 在指定目录中查找匹配日期的日志文件
func (s *Service) findLogFilesInDirectory(logDir, dateStr string) ([]string, error) {
	// 检查目录是否存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		return []string{}, nil // 目录不存在时返回空数组，不报错
	}

	// 读取目录中的所有文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return nil, fmt.Errorf("读取目录失败: %w", err)
	}

	var matchingFiles []string
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		// 检查文件名是否包含指定日期（格式：YYYY-MM-DD）
		if strings.Contains(fileName, dateStr) {
			fullPath := filepath.Join(logDir, fileName)
			matchingFiles = append(matchingFiles, fullPath)
		}
	}

	return matchingFiles, nil
}

// calculateFilesSize 计算文件列表的总大小（字节）
func (s *Service) calculateFilesSize(filePaths []string) int64 {
	var totalSize int64

	for _, filePath := range filePaths {
		if fileInfo, err := os.Stat(filePath); err == nil {
			totalSize += fileInfo.Size()
		} else {
			log.Printf("⚠️  获取文件 %s 大小失败: %v", filepath.Base(filePath), err)
		}
	}

	return totalSize
}

// createDefaultResourceUsageForTrafficCalculation 为流量计算创建默认的资源使用记录
func (s *Service) createDefaultResourceUsageForTrafficCalculation(businessID string) *models.ServiceResourceUsage {
	currentTime := utils.GetCSTTime() // 使用东八区时间

	// 设置默认的 NetworkTrafficUpdatedAt 为7天前
	// 这样确保新业务会被包含在流量统计中
	defaultTrafficTime := currentTime.AddDate(0, 0, -7)

	defaultUsage := &models.ServiceResourceUsage{
		BusinessID:              businessID,
		OSSNetworkTraffic:       0.0,                 // 初始流量为0
		OSSNetworkTrafficByDay:  0.0,                 // 初始前一天流量为0
		NetworkTrafficUpdatedAt: &defaultTrafficTime, // 设置为7天前
		OSSDiskUsage:            0,                   // 初始存储为0
		LastAccessTime:          &currentTime,
	}

	// 创建元数据
	metadata := &models.ResourceUsageMetadata{
		CollectionTime: currentTime,
		DataSources:    []string{"auto_created_for_traffic_calculation"},
		CustomFields: map[string]interface{}{
			"creation_reason": "新业务自动创建用于流量统计",
			"auto_created":    true,
		},
	}

	// 设置元数据
	if err := defaultUsage.SetMetadata(metadata); err != nil {
		log.Printf("⚠️  设置默认记录元数据失败: %v", err)
	}

	log.Printf("🔧 为业务 %s 创建默认资源记录: NetworkTrafficUpdatedAt=%s",
		businessID, defaultTrafficTime.Format("2006-01-02 15:04:05"))

	return defaultUsage
}

// isBusinessTrafficMatch 判断文件路径是否属于指定业务的流量
// 🚀 优化：直接基于 business_id 的匹配逻辑（OSS路径已基于 business_id）
func (s *Service) isBusinessTrafficMatch(filePath, businessID string) bool {
	// 检查文件路径是否包含业务ID
	// 可能的格式：
	// 1. /business_id/...
	// 2. /path/business_id/...
	// 3. 文件名包含business_id

	// 简单的包含检查
	if strings.Contains(filePath, businessID) {
		return true
	}

	// 检查路径段
	pathSegments := strings.Split(strings.Trim(filePath, "/"), "/")
	for _, segment := range pathSegments {
		if segment == businessID {
			return true
		}
	}

	return false
}

// shouldSkipBusinessWithUsage 判断是否应该跳过业务的流量统计（基于 business_id 优化版本）
// 基于已获取的 ServiceResourceUsage 记录和新的时间关系定义判断是否需要处理
func (s *Service) shouldSkipBusinessWithUsage(businessID string, existingUsage *models.ServiceResourceUsage, currentTime time.Time) bool {
	// 首次处理，不跳过
	if existingUsage == nil || existingUsage.NetworkTrafficUpdatedAt == nil {
		log.Printf("📝 业务 %s: 首次处理，不跳过", businessID)
		return false
	}

	// 🔧 修复时区问题：使用本地时区的日期截断，避免 Truncate 的 UTC 对齐问题
	lastUpdatedDate := time.Date(existingUsage.NetworkTrafficUpdatedAt.Year(),
		existingUsage.NetworkTrafficUpdatedAt.Month(),
		existingUsage.NetworkTrafficUpdatedAt.Day(),
		0, 0, 0, 0, existingUsage.NetworkTrafficUpdatedAt.Location())
	currentDate := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
		0, 0, 0, 0, currentTime.Location())

	// 新的判断逻辑：
	// NetworkTrafficUpdatedAt == currentTime: 所有文件都处理完了，跳过
	// NetworkTrafficUpdatedAt < currentTime: 需要处理 NetworkTrafficUpdatedAt 时间的日志，不跳过
	// NetworkTrafficUpdatedAt > currentTime: 不可能情况（时钟回拨）

	if lastUpdatedDate.Equal(currentDate) {
		// 情况a: 所有文件都处理完了
		log.Printf("⏭️  跳过业务 %s: 所有日志都已处理完 (last_updated: %s == current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return true
	} else if lastUpdatedDate.Before(currentDate) {
		// 情况b: 需要处理 NetworkTrafficUpdatedAt 时间的日志
		log.Printf("📝 业务 %s: 需要处理 %s 的日志 (last_updated: %s < current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"),
			lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false
	} else {
		// 情况c: 时钟回拨异常
		log.Printf("⚠️  业务 %s: 异常情况 - 时钟回拨? (last_updated: %s > current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false // 保守处理，不跳过
	}
}

// 🆕 资源监控阈值常量
const (
	CPU_ALERT_THRESHOLD    = 0.85 // CPU使用率告警阈值 85%
	MEMORY_ALERT_THRESHOLD = 0.85 // 内存使用率告警阈值 85%
)

// 🆕 checkNodeResourceUsage 检查节点资源使用率并发送告警通知
func (s *Service) checkNodeResourceUsage(worker *models.WorkerInfo, nodeIndex int) {
	// 检查索引有效性
	if nodeIndex < 0 || nodeIndex >= len(worker.Nodes) {
		log.Printf("Invalid node index %d for worker %s", nodeIndex, worker.WorkerID)
		return
	}

	node := worker.Nodes[nodeIndex]

	// 计算总资源（CPU和Memory字段表示总量）
	cpuTotal := node.CPU
	memoryTotal := float64(node.Memory)

	// 避免除零错误
	if cpuTotal <= 0 || memoryTotal <= 0 {
		log.Printf("Invalid node resource totals for node %s: CPU=%.2f, Memory=%.0f",
			node.IP, cpuTotal, memoryTotal)
		return
	}

	// 计算使用率
	cpuUsageRate := node.Cpu_allocation / cpuTotal
	memoryUsageRate := float64(node.Memory_allocation) / memoryTotal

	log.Printf("Node %s resource usage: CPU=%.1f%% (%.2f/%.2f), Memory=%.1f%% (%d/%.0f)",
		node.IP, cpuUsageRate*100, node.Cpu_allocation, cpuTotal,
		memoryUsageRate*100, node.Memory_allocation, memoryTotal)

	// 检查是否需要告警
	needAlert := cpuUsageRate >= CPU_ALERT_THRESHOLD || memoryUsageRate >= MEMORY_ALERT_THRESHOLD

	if needAlert {
		// 构建告警key用于防重复
		alertKey := fmt.Sprintf("node_resource_%s_%s", worker.WorkerID, node.IP)

		// 发送告警（NotificationManager内部会处理去重）
		s.sendNodeResourceAlert(node, cpuUsageRate, memoryUsageRate, cpuTotal, memoryTotal, worker.WorkerID)

		log.Printf("⚠️  节点 %s 资源使用率超过阈值: CPU=%.1f%%, Memory=%.1f%% (告警key: %s)",
			node.IP, cpuUsageRate*100, memoryUsageRate*100, alertKey)
	}
}

// 🆕 sendNodeResourceAlert 发送节点资源使用率告警通知
func (s *Service) sendNodeResourceAlert(node models.Node, cpuRate, memoryRate, cpuTotal, memoryTotal float64, workerID string) {
	// 确定告警级别和原因
	var alertReasons []string
	alertLevel := utils.NotificationLevelWarning

	if cpuRate >= CPU_ALERT_THRESHOLD {
		alertReasons = append(alertReasons, fmt.Sprintf("CPU使用率 %.1f%% ≥ %.0f%%", cpuRate*100, CPU_ALERT_THRESHOLD*100))
	}
	if memoryRate >= MEMORY_ALERT_THRESHOLD {
		alertReasons = append(alertReasons, fmt.Sprintf("内存使用率 %.1f%% ≥ %.0f%%", memoryRate*100, MEMORY_ALERT_THRESHOLD*100))
	}

	// 如果使用率超过90%，升级为错误级别
	if cpuRate >= 0.9 || memoryRate >= 0.9 {
		alertLevel = utils.NotificationLevelError
	}

	title := fmt.Sprintf("[S:%s] 节点资源使用率告警 %s => %s", s.GetServiceIP(), workerID, node.IP)
	content := fmt.Sprintf(
		"🚨 告警原因: %s\n\n"+
			"📊 资源使用详情:\n"+
			"• CPU: %.1f%% (%.2f/%.2f cores)\n"+
			"• 内存: %.1f%% (%d/%.0f MB)\n\n"+
			"⚠️  告警阈值: CPU ≥ %.0f%%, 内存 ≥ %.0f%%",
		strings.Join(alertReasons, ", "),
		cpuRate*100, node.Cpu_allocation, cpuTotal,
		memoryRate*100, node.Memory_allocation, memoryTotal,
		CPU_ALERT_THRESHOLD*100, MEMORY_ALERT_THRESHOLD*100,
	)

	log.Printf("🚨 发送节点资源告警: %s CPU=%.1f%%, Memory=%.1f%% (阈值: %.0f%%)",
		node.IP, cpuRate*100, memoryRate*100, CPU_ALERT_THRESHOLD*100)

	s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
		Level:     alertLevel,
		Title:     title,
		Content:   content,
		ServiceID: "resource-monitor",
		Metadata: map[string]interface{}{
			"worker_id":        workerID,
			"node_ip":          node.IP,
			"cpu_rate":         cpuRate,
			"memory_rate":      memoryRate,
			"cpu_threshold":    CPU_ALERT_THRESHOLD,
			"memory_threshold": MEMORY_ALERT_THRESHOLD,
			"alert_type":       "node_resource_usage",
		},
		Timestamp: utils.GetCSTTimeString(),
	})
}

// 🆕 createUpdateCandidate 为UPDATE操作创建固定的候选节点
func (s *Service) createUpdateCandidate(record models.DeployRecord, workers []models.WorkerInfo) *NodeCandidate {
	log.Printf("Creating UPDATE candidate for service %s: Worker=%s, Node=%s",
		record.ServiceID, record.WorkerID, record.NodeIP)

	// 1. 查找原Worker是否存在且可用
	var originalWorker *models.WorkerInfo
	for i := range workers {
		if workers[i].WorkerID == record.WorkerID {
			originalWorker = &workers[i]
			break
		}
	}

	if originalWorker == nil {
		log.Printf("Original worker %s not found in available workers list", record.WorkerID)
		return nil
	}

	// 2. 检查原节点是否在Worker的节点列表中
	var originalNode *models.Node
	for i := range originalWorker.Nodes {
		if originalWorker.Nodes[i].IP == record.NodeIP {
			originalNode = &originalWorker.Nodes[i]
			break
		}
	}

	if originalNode == nil {
		log.Printf("Original node %s not found in worker %s nodes list", record.NodeIP, record.WorkerID)
		return nil
	}

	// 3. 构造候选节点（UPDATE， 暂时不考虑资源的变换，通过定时资源更新用量问题）
	// TODO： 需要优化，但是无思路
	candidate := &NodeCandidate{
		Worker: *originalWorker,
		NodeIP: record.NodeIP,
	}

	log.Printf("✅ Successfully created UPDATE candidate: Worker=%s, Node=%s",
		record.WorkerID, record.NodeIP)
	return candidate
}
